body {
  color: var(--td-text-color-secondary);
  font-family: -apple-system, BlinkMacSystemFont, PingFang SC, Microsoft YaHei, Arial Regular;
  font: var(--td-font-body-medium);
  -webkit-font-smoothing: antialiased;
  padding: 0;
  margin: 0;
}

pre {
  font-family: var(--td-font-family);
}

ul,
dl,
li,
dd,
dt {
  margin: 0;
  padding: 0;
  list-style: none;
}

figure,
h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

* {
  box-sizing: border-box;
}

.t-button-link,
a {
  color: var(--td-brand-color);
  text-decoration: none;
  margin-right: var(--td-comp-margin-xxl);
  cursor: pointer;
  transition: color @anim-duration-base @anim-time-fn-easing;

  &:hover {
    color: var(--td-brand-color-hover);
  }

  &:active {
    color: var(--td-brand-color-active);
  }

  &--active {
    color: var(--td-brand-color-active);
  }

  &:focus {
    text-decoration: none;
  }

  &:last-child {
    margin-right: 0;
  }

  &:last-child {
    margin-right: 0;
  }
}

.container-base-margin-top {
  margin-top: 16px;
}

.card-date-picker-container {
  width: 250px;
}
