<template>
  <div :class="sideNavCls">
    <t-menu
      width="232px"
      :class="menuCls"
      :theme="theme"
      :value="active"
      :collapsed="collapsed"
      :defaultExpanded="defaultExpanded"
    >
      <template #logo>
        <span v-if="showLogo" :class="`${prefix}-side-nav-logo-wrapper modern-logo-wrapper`" @click="() => handleNav('/dashboard/base')">
<!--          <component :is="getLogo" :class="`${prefix}-side-nav-logo-${collapsed ? 't' : 'tdesign'}-logo`" />-->
          <div class="logo-container">
            <img v-if="seet.site_img" :src="seet.site_img" class="logo-image">
            <span v-if="!collapsed" class="logo-text" :title="seet.site_name">{{seet.site_name}}</span>
          </div>
        </span>
      </template>
      <menu-content :navData="menu" />
<!--      <template #operations>-->
<!--        <span class="version-container"> {{ !collapsed ? `TDesign Starter ${pgk.version}` : pgk.version }} </span>-->
<!--      </template>-->
    </t-menu>
    <div :class="`${prefix}-side-nav-placeholder${collapsed ? '-hidden' : ''}`"></div>
  </div>
</template>

<script lang="ts">
import Vue from 'vue';
import { prefix } from '@/config/global';
import { ClassName } from '@/interface';
import Logo from '@/assets/assets-t-logo.svg';
import LogoFull from '@/assets/assets-logo-full.svg';

import MenuContent from './MenuContent.vue';
import pgk from '../../../package.json';

const MIN_POINT = 992 - 1;

export default Vue.extend({
  name: 'sideNav',
  components: {
    MenuContent,
  },
  props: {
    menu: Array,
    showLogo: {
      type: Boolean,
      default: true,
    },
    isFixed: {
      type: Boolean,
      default: true,
    },
    layout: String,
    headerHeight: {
      type: String,
      default: '56px',
    },
    theme: {
      type: String,
      default: 'light',
    },
    isCompact: {
      type: Boolean,
      default: false,
    },
    maxLevel: {
      type: Number,
      default: 3,
    },
  },
  data() {
    return {
      prefix,
      pgk,
      seet:{}
    };
  },
  computed: {
    defaultExpanded() {
      const path = this.active;
      const parentPath = path.substring(0, path.lastIndexOf('/'));
      if (parentPath.lastIndexOf('/')) {
        const threeLevel = parentPath.substring(0, parentPath.lastIndexOf('/'));
        return threeLevel === '' ? [] : [threeLevel, parentPath];
      }
      return parentPath === '' ? [] : [parentPath];
    },
    iconName(): string {
      return this.$store.state.setting.isSidebarCompact ? 'menu-fold' : 'menu-unfold';
    },
    collapsed(): boolean {
      return this.$store.state.setting.isSidebarCompact;
    },
    sideNavCls(): Array<ClassName> {
      return [
        `${this.prefix}-sidebar-layout`,
        {
          [`${this.prefix}-sidebar-compact`]: this.isCompact,
        },
      ];
    },
    menuCls(): Array<ClassName> {
      return [
        `${this.prefix}-side-nav`,
        {
          [`${this.prefix}-side-nav-no-logo`]: !this.showLogo,
          [`${this.prefix}-side-nav-no-fixed`]: !this.isFixed,
          [`${this.prefix}-side-nav-mix-fixed`]: this.layout === 'mix' && this.isFixed,
        },
      ];
    },
    layoutCls(): Array<ClassName> {
      return [`${this.prefix}-side-nav-${this.layout}`, `${this.prefix}-sidebar-layout`];
    },
    active(): string {
      if (!this.$route.path) {
        return '';
      }
      return this.$route.path
        .split('/')
        .filter((_item: string, index: number) => index <= this.maxLevel && index > 0)
        .map((item: string) => `/${item}`)
        .join('');
    },
    getLogo() {
      if (this.collapsed) {
        return Logo;
      }
      return LogoFull;
    },
  },
  mounted() {
    this.autoCollapsed();
    this.getSeet();
    window.onresize = () => {
      this.autoCollapsed();
    };
  },
  methods: {
    getSeet() {
      this.$request
        .post("/login/Seet")
        .then((res) => {
          this.seet=res.settings;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    changeCollapsed(): void {
      this.$store.commit('setting/toggleSidebarCompact');
    },
    autoCollapsed(): void {
      const isCompact = window.innerWidth <= MIN_POINT;
      this.$store.commit('setting/showSidebarCompact', isCompact);
    },
    handleNav(url: string) {
      this.$router.push(url);
    },
  },
});
</script>

<style lang="less" scoped>
// ==================== 现代化侧边栏样式 ====================

// 现代化LOGO区域
.modern-logo-wrapper {
  border-bottom: 1px solid rgba(33, 150, 243, 0.1) !important;
  background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%) !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  width: 232px !important;
  box-sizing: border-box !important;

  &:hover {
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%) !important;
    transform: translateY(-1px) !important;
  }

  .logo-container {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    min-width: 0;
    max-width: 100%;
    overflow: hidden;

    .logo-image {
      width: 50px !important;
      height: 50px !important;
      border-radius: 12px !important;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15) !important;
      transition: all 0.3s ease !important;
      object-fit: cover !important;
      flex-shrink: 0 !important;
      margin-top: 2px !important;

      &:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 6px 16px rgba(33, 150, 243, 0.25) !important;
      }
    }

    .logo-text {
      font-size: 1.25rem !important;
      font-weight: 700 !important;
      color: #1976D2 !important;
      text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;
      transition: all 0.3s ease !important;
      flex: 1 !important;
      min-width: 0 !important;
      width: calc(232px - 32px - 50px - 12px) !important;
      max-width: calc(232px - 32px - 50px - 12px) !important;
      line-height: 1.3 !important;
      word-wrap: break-word !important;
      word-break: break-word !important;
      overflow-wrap: break-word !important;
      white-space: normal !important;
      hyphens: auto !important;

      &:hover {
        color: #2196F3 !important;
      }
    }
  }
}

// 侧边栏容器样式覆盖
:deep(.t-layout__sider) {
  background: linear-gradient(180deg, #ffffff 0%, #f8fbff 100%) !important;
  border-right: 1px solid rgba(33, 150, 243, 0.1) !important;
  box-shadow: 4px 0 12px rgba(33, 150, 243, 0.08) !important;
}

// 菜单容器样式覆盖
:deep(.t-menu) {
  background: transparent !important;
  border: none !important;
  padding: 16px 0 !important;

  // 菜单项基础样式
  .t-menu__item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    position: relative !important;
    overflow: hidden !important;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.1), transparent);
      transition: left 0.5s;
    }

    &:hover {
      background: rgba(33, 150, 243, 0.1) !important;
      color: #1976D2 !important;
      transform: translateX(4px) !important;
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1) !important;

      &::before {
        left: 100%;
      }
    }

    &.t-is-active {
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
      transform: translateX(4px) !important;

      &::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 60%;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 2px 0 0 2px;
      }
    }
  }

  // 子菜单样式
  .t-submenu {
    .t-submenu__title {
      border-radius: 8px !important;
      margin: 2px 8px !important;
      transition: all 0.3s ease !important;
      font-weight: 500 !important;
      position: relative !important;
      overflow: hidden !important;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(33, 150, 243, 0.1), transparent);
        transition: left 0.5s;
      }

      &:hover {
        background: rgba(33, 150, 243, 0.1) !important;
        color: #1976D2 !important;
        transform: translateX(4px) !important;
        box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1) !important;

        &::before {
          left: 100%;
        }
      }

      &.t-is-active {
        background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
        transform: translateX(4px) !important;
      }
    }

    .t-submenu__content {
      background: rgba(248, 251, 255, 0.5) !important;
      border-radius: 0 0 8px 8px !important;
      margin: 0 8px !important;
      padding: 8px 0 !important;
      border: 1px solid rgba(33, 150, 243, 0.1) !important;
      border-top: none !important;
      backdrop-filter: blur(10px) !important;

      .t-menu__item {
        margin: 2px 12px !important;
        font-size: 0.9rem !important;
        padding-left: 24px !important;
        position: relative !important;

        &::before {
          content: '';
          position: absolute;
          left: 12px;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          background: rgba(33, 150, 243, 0.3);
          border-radius: 50%;
          transition: all 0.3s ease;
        }

        &:hover {
          background: rgba(33, 150, 243, 0.08) !important;
          transform: translateX(2px) !important;

          &::before {
            background: #2196F3;
            transform: translateY(-50%) scale(1.2);
          }
        }

        &.t-is-active {
          background: rgba(33, 150, 243, 0.15) !important;
          color: #1976D2 !important;
          font-weight: 600 !important;
          transform: translateX(2px) !important;

          &::before {
            background: #1976D2;
            transform: translateY(-50%) scale(1.5);
          }
        }
      }
    }
  }

  // 图标样式
  .t-icon {
    font-size: 1.1rem !important;
    margin-right: 8px !important;
    transition: all 0.3s ease !important;
  }

  // 展开/收起箭头
  .t-submenu__icon {
    transition: all 0.3s ease !important;
    color: rgba(33, 150, 243, 0.6) !important;

    &.t-submenu__icon--active {
      color: #2196F3 !important;
      transform: rotate(180deg) !important;
    }
  }
}

// 收起状态样式
:deep(.t-menu--collapsed) {
  .t-menu__item,
  .t-submenu__title {
    justify-content: center !important;

    &:hover {
      transform: translateX(0) scale(1.05) !important;
    }

    &.t-is-active {
      transform: translateX(0) scale(1.05) !important;
    }
  }
}

// 响应式优化
@media (max-width: 992px) {
  .modern-logo-wrapper {
    padding: 16px 12px !important;

    .logo-container {
      gap: 8px;

      .logo-image {
        width: 40px !important;
        height: 40px !important;
      }

      .logo-text {
        font-size: 1.1rem !important;
      }
    }
  }
}

// 处理超长站点名称
@media (max-width: 280px) {
  .modern-logo-wrapper {
    .logo-container {
      .logo-text {
        font-size: 0.9rem !important;
      }
    }
  }
}

// 侧边栏宽度较小时的处理
@media (max-width: 220px) {
  .modern-logo-wrapper {
    .logo-container {
      .logo-text {
        font-size: 0.9rem !important;
        line-height: 1.2 !important;
      }
    }
  }

  :deep(.t-menu) {
    padding: 12px 0 !important;

    .t-menu__item,
    .t-submenu__title {
      margin: 1px 6px !important;
      font-size: 0.9rem !important;
    }
  }
}

// 动画效果
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modern-logo-wrapper {
  animation: slideInLeft 0.6s ease-out;
}

:deep(.t-menu__item),
:deep(.t-submenu__title) {
  animation: slideInLeft 0.4s ease-out;
}
</style>
