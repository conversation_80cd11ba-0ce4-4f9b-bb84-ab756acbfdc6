<template>
  <div>
    <template v-for="item in list">
      <template v-if="!item.children || !item.children.length || item.meta?.single">
        <t-menu-item
          v-if="getHref(item)"
          :key="`href${item.path}`"
          :href="getHref(item)?.[0]"
          :name="item.path"
          :value="item.meta?.single ? item.redirect || item.path : item.path"
        >
          <template #icon>
            <t-icon v-if="typeof item.icon === 'string' && item.icon" :name="item.icon" />
            <renderFnIcon :item="item" />
          </template>
          <span class="menu-item-content">
            <span class="menu-text">{{ item.title }}</span>
          </span>
        </t-menu-item>
        <t-menu-item
          v-else
          :key="`${item.path}`"
          :to="item.path"
          :name="item.path"
          :value="item.meta?.single ? item.redirect || item.path : item.path"
        >
          <template #icon>
            <t-icon v-if="typeof item.icon === 'string' && item.icon" :name="item.icon" />
            <renderFnIcon :item="item" />
          </template>
          <span class="menu-item-content">
            <span class="menu-text">{{ item.title }}</span>
          </span>
        </t-menu-item>
      </template>
      <t-submenu v-else :name="item.path" :value="item.path" :key="item.path" class="modern-submenu">
        <template #title>
          <span class="menu-item-content">
            <span class="menu-text">{{ item.title }}</span>
          </span>
        </template>
        <template #icon>
          <t-icon v-if="typeof item.icon === 'string' && item.icon" :name="item.icon" />
          <renderFnIcon :item="item" />
        </template>
        <menu-content v-if="item.children" :nav-data="item.children" />
      </t-submenu>
    </template>
  </div>
</template>

<script lang="ts">
import Vue, { PropType } from 'vue';

import { prefix } from '@/config/global';
import { MenuRoute } from '@/interface';

const getMenuList = (list: MenuRoute[], basePath?: string): MenuRoute[] => {
  if (!list) {
    return [];
  }

  return list
    .map((item) => {
      const path = basePath && !item.path.includes(basePath) ? `${basePath}/${item.path}` : item.path;
      return {
        path,
        title: item.meta?.title,
        icon: item.meta?.icon || '',
        children: getMenuList(item.children, path),
        meta: item.meta,
        redirect: item.redirect,
      };
    })
    .filter((item) => item.meta && item.meta.hidden !== true);
};

Vue.component('renderFnIcon', {
  props: {
    item: {
      type: Object as PropType<MenuRoute>,
      required: true,
    },
  },
  // 遵循最小改动的原则，这里仍然使用 createElement
  render(createElement) {
    if (typeof this.item.icon === 'function' || (this.item.icon && typeof this.item.icon.render === 'function')) {
      return createElement(this.item.icon, {
        class: 't-icon',
      });
    }
    return undefined;
  },
});

export default Vue.extend({
  name: 'MenuContent',
  props: {
    navData: Array,
  },
  data() {
    return {
      prefix,
    };
  },
  computed: {
    list(): Array<MenuRoute> {
      return getMenuList(this.navData);
    },
  },
  methods: {
    getHref(item: MenuRoute) {
      return item.path.match(/(http|https):\/\/([\w.]+\/?)\S*/);
    },
    getMenuEmoji(title: string): string {
      const emojiMap: { [key: string]: string } = {
        '首页': '🏠',
        '数据统计': '📊',
        '用户管理': '👥',
        '用户列表': '👤',
        '新增用户': '➕',
        '编辑用户': '✏️',
        '平台管理': '🏢',
        '平台列表': '📋',
        '新增平台': '🆕',
        '编辑平台': '📝',
        '上传文件': '📤',
        '客服管理': '💬',
        '客服设置': '⚙️',
        '公告管理': '📢',
        '公告列表': '📄',
        '新建公告': '📝',
        '编辑公告': '✏️',
        '查看公告': '👁️',
        '网站设置': '🌐',
        '系统信息': '🖥️',
        '日志信息': '📊',
        '账号安全': '🔐',
        '修改密码': '🔑',
        '修改账号': '👤',
        '修改二级密码': '🔐',
        '模块管理': '🧩',
        '模块列表': '📦',
        '新增模块': '➕',
        '编辑模块': '✏️',
        '帮助文档': '📚',
        '系统设置': '⚙️',
        '权限管理': '🔒',
        '角色管理': '👑',
        '菜单管理': '📋',
        '操作日志': '📝',
        '登录日志': '🔑',
        '系统监控': '📈',
        '性能监控': '⚡',
        '错误日志': '❌',
        '备份管理': '💾',
        '数据备份': '🗄️',
        '文件管理': '📁',
        '上传管理': '📤',
        '下载管理': '📥',
      };

      return emojiMap[title] || '📄';
    },
  },
});
</script>

<style lang="less" scoped>
// ==================== 现代化菜单样式 ====================

// 菜单项内容
.menu-item-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;

  .menu-emoji {
    font-size: 1.1rem;
    flex-shrink: 0;
    width: 20px;
    text-align: center;
  }

  .menu-text {
    flex: 1;
    font-weight: 500;
  }
}

// 现代化子菜单
.modern-submenu {
  :deep(.t-submenu__title) {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: rgba(33, 150, 243, 0.1) !important;
      color: #1976D2 !important;
    }
  }

  :deep(.t-submenu__content) {
    background: rgba(248, 251, 255, 0.5) !important;
    border-radius: 0 0 8px 8px !important;
    margin: 0 8px !important;
    padding: 4px 0 !important;
  }
}

// 全局菜单样式覆盖
:deep(.t-menu) {
  background: transparent !important;
  border: none !important;

  .t-menu__item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;

    &:hover {
      background: rgba(33, 150, 243, 0.1) !important;
      color: #1976D2 !important;
      transform: translateX(4px) !important;
    }

    &.t-is-active {
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
      color: white !important;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
      transform: translateX(4px) !important;

      .menu-emoji {
        filter: brightness(1.2) !important;
      }
    }
  }

  .t-submenu {
    .t-submenu__title {
      border-radius: 8px !important;
      margin: 2px 8px !important;
      transition: all 0.3s ease !important;
      font-weight: 500 !important;

      &:hover {
        background: rgba(33, 150, 243, 0.1) !important;
        color: #1976D2 !important;
        transform: translateX(4px) !important;
      }

      &.t-is-active {
        background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
        color: white !important;
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
        transform: translateX(4px) !important;
      }
    }

    .t-submenu__content {
      background: rgba(248, 251, 255, 0.5) !important;
      border-radius: 0 0 8px 8px !important;
      margin: 0 8px !important;
      padding: 4px 0 !important;
      border: 1px solid rgba(33, 150, 243, 0.1) !important;
      border-top: none !important;

      .t-menu__item {
        margin: 2px 12px !important;
        font-size: 0.9rem !important;
        padding-left: 24px !important;

        &:hover {
          background: rgba(33, 150, 243, 0.08) !important;
          transform: translateX(2px) !important;
        }

        &.t-is-active {
          background: rgba(33, 150, 243, 0.15) !important;
          color: #1976D2 !important;
          font-weight: 600 !important;
          transform: translateX(2px) !important;
        }
      }
    }
  }

  .t-icon {
    font-size: 1.1rem !important;
    margin-right: 8px !important;
    transition: all 0.3s ease !important;
  }
}

// 菜单项动画效果
@keyframes menuItemSlide {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

:deep(.t-menu__item),
:deep(.t-submenu__title) {
  animation: menuItemSlide 0.3s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .menu-item-content {
    gap: 6px;

    .menu-emoji {
      font-size: 1rem;
      width: 18px;
    }

    .menu-text {
      font-size: 0.9rem;
    }
  }
}
</style>
