<template>
  <div>
    <t-drawer
      size="408px"
      :footer="false"
      :visible.sync="showSettingPanel"
      header="💬 系统客服"
      :closeBtn="true"
      :onCloseBtnClick="handleCloseDrawer"
      class="setting-drawer-container modern-drawer"
    >
      <div class="setting-container modern-setting-container">
        <!-- 客服联系方式 -->
        <div class="contact-section">
          <div class="contact-card">
            <div class="contact-icon">📞</div>
            <div class="contact-content">
              <div class="contact-label">客服电话</div>
              <div class="contact-value">
                {{ customer.phone!=''?customer.phone:'暂无' }}
              </div>
            </div>
          </div>

          <div class="contact-card">
            <div class="contact-icon">💬</div>
            <div class="contact-content">
              <div class="contact-label">客服QQ</div>
              <div class="contact-value">
                {{ customer.qq!=''?customer.qq:'暂无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 客服二维码 -->
        <div class="qr-section">
          <div class="section-title">
            <span class="title-icon">📱</span>
            <span class="title-text">客服二维码</span>
          </div>

          <div class="qr-container">
            <div v-if="customer.qr_img==''&&customer.qr_img_tow==''" class="no-qr">
              <div class="no-qr-icon">📭</div>
              <div class="no-qr-text">暂无二维码</div>
            </div>

            <div v-else class="qr-grid">
              <div v-if="customer.qr_img!=''" class="qr-card">
                <img :src="customer.qr_img" class="qr-image" alt="客服二维码1"/>
                <div class="qr-label">客服二维码①</div>
              </div>
              <div v-if="customer.qr_img_tow!=''" class="qr-card">
                <img :src="customer.qr_img_tow" class="qr-image" alt="客服二维码2"/>
                <div class="qr-label">客服二维码②</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 帮助文档区域 -->
        <div class="help-section" v-if="$store.state.user.admin==1">
          <div class="help-card">
            <div class="help-icon">📚</div>
            <div class="help-content">
              <div class="help-title">系统帮助文档</div>
              <div class="help-desc">查看详细的系统使用说明</div>
            </div>
            <t-button
              theme="primary"
              @click="openUrl()"
              variant="outline"
              class="help-btn"
              size="small"
            >
              <span class="btn-icon">🔗</span>
              <span>查看文档</span>
            </t-button>
          </div>
        </div>
      </div>
    </t-drawer>
  </div>
</template>
<script lang="ts">
import {mapGetters} from 'vuex';
import {Color} from 'tvision-color';
import {PopupVisibleChangeContext} from 'tdesign-vue';

import STYLE_CONFIG from '@/config/style';
import {insertThemeStylesheet, generateColorMap} from '@/utils/color';
import {DEFAULT_COLOR_OPTIONS} from '@/config/color';

import Thumbnail from '@/components/thumbnail/index.vue';
import ColorContainer from '@/components/color/index.vue';

import SettingDarkIcon from '@/assets/assets-setting-dark.svg';
import SettingLightIcon from '@/assets/assets-setting-light.svg';
import SettingAutoIcon from '@/assets/assets-setting-auto.svg';

const LAYOUT_OPTION = ['side', 'top', 'mix'];

const MODE_OPTIONS = [
  {type: 'light', text: '明亮'},
  {type: 'dark', text: '暗黑'},
  {type: 'auto', text: '跟随系统'},
];

export default {
  name: 'DefaultLayoutSetting',
  components: {Thumbnail, ColorContainer},
  data() {
    return {
      colors: {
        hex: null,
      },
      MODE_OPTIONS,
      LAYOUT_OPTION,
      DEFAULT_COLOR_OPTIONS,
      visible: false,
      formData: {...STYLE_CONFIG},
      isColoPickerDisplay: false,
      customer: {}
    };
  },
  computed: {
    ...mapGetters('setting', ['showSettingBtn']),
    showSettingPanel: {
      get() {
        return this.$store.state.setting.showSettingPanel;
      },
      set(newVal) {
        this.$store.commit('setting/toggleSettingPanel', newVal);
      },
    },
    iconName() {
      return this.visible ? 'close' : 'setting';
    },
    showOthers() {
      return (this.formData.showFooter && !this.formData.isSidebarFixed) || !this.formData.splitMenu;
    },
    dynamicColor() {
      const isDynamic = DEFAULT_COLOR_OPTIONS.indexOf(this.formData.brandTheme) === -1;
      return isDynamic ? this.formData.brandTheme : '';
    },
  },
  watch: {
    formData: {
      handler(newVal) {
        if (!newVal.brandTheme) return;
        // 没有在formData中 需要从store中同步过来
        const {isSidebarCompact} = this.$store.state.setting;
        this.$store.dispatch('setting/changeTheme', {...newVal, isSidebarCompact});
      },
      deep: true,
    },
  },
  mounted() {
    document.querySelector('.dynamic-color-btn')?.addEventListener('click', () => {
      this.isColoPickerDisplay = true;
    });
    this.getCustomer();
  },
  methods: {
    openUrl(){
      window.open('https://www.kancloud.cn/@yuluo_6');
    },
    getCustomer() {
      this.$request
        .post("/index/getCustomer",)
        .then((res) => {
          console.log(res);
          this.customer = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onPopupVisibleChange(visible: boolean, context: PopupVisibleChangeContext) {
      if (!visible && context.trigger === 'document') this.isColoPickerDisplay = visible;
    },
    onReset(): void {
      this.formData = {
        ...STYLE_CONFIG,
      };
      this.$message.success('已恢复初始设置');
    },
    onSubmit({result, firstError, e}): void {
      e.preventDefault();
      if (result === true) {
        this.visible = false;
      } else {
        this.$message.warning(firstError);
      }
    },
    getModeIcon(mode: string) {
      if (mode === 'light') {
        return SettingLightIcon;
      }
      if (mode === 'dark') {
        return SettingDarkIcon;
      }
      return SettingAutoIcon;
    },
    getThumbnailUrl(name: string) {
      return `https://tdesign.gtimg.com/starter/setting/${name}.png`;
    },
    handleClick(): void {
      this.$store.commit('setting/toggleSettingPanel', true);
    },
    handleCloseDrawer(): void {
      this.$store.commit('setting/toggleSettingPanel', false);
    },
    handleCopy(): void {
      const text = JSON.stringify(this.formData, null, 4);
      this.$copyText(text).then(() => {
        this.$message.closeAll();
        this.$message.success('复制成功');
      });
    },
    changeColor(hex: string) {
      const {setting} = this.$store.state;

      const {colors: newPalette, primary: brandColorIndex} = Color.getColorGradations({
        colors: [hex],
        step: 10,
        remainInput: false, // 是否保留输入 不保留会矫正不合适的主题色
      })[0];

      const {mode} = this.$store.state.setting;
      const colorMap = generateColorMap(hex, newPalette, mode, brandColorIndex);
      this.formData.brandTheme = hex;

      this.$store.commit('setting/addColor', {[hex]: colorMap});
      this.$store.dispatch('setting/changeTheme', {...setting, brandTheme: hex});

      insertThemeStylesheet(hex, colorMap, mode);
    },
  },
};
</script>
<style lang="less">
@import '@/style/variables.less';

.tdesign-setting {
  z-index: 100;
  position: fixed;
  bottom: 200px;
  right: 0;
  transition: transform 0.3s cubic-bezier(0.7, 0.3, 0.1, 1), visibility 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);
  height: 40px;
  width: 40px;
  border-radius: 20px 0 0 20px;
  transition: all 0.3s;

  .t-icon {
    margin-left: 8px;
  }

  .tdesign-setting-text {
    font-size: 12px;
    display: none;
  }

  &:hover {
    width: 96px;

    .tdesign-setting-text {
      display: inline-block;
    }
  }
}

.setting-layout-color-group {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50% !important;
  padding: 6px !important;
  border: 2px solid transparent !important;

  > .t-radio-button__label {
    display: inline-flex;
  }
}

.tdesign-setting-close {
  position: fixed;
  bottom: 200px;
  right: 300px;
}

.setting-group-title {
  font-size: 16px;
  line-height: 22px;
  margin: 15px 0 15px 0;
  text-align: left;
  font-family: PingFang SC;
  font-style: normal;
  font-weight: 600;
  color: var(--td-text-color-primary);
}

.setting-group-color {
  position: relative;

  > div {
    position: absolute;
    z-index: 2;
    right: 0;
  }
}

.setting-link {
  cursor: pointer;
  color: var(--td-brand-color);
  margin-bottom: 8px;
}

.setting-info {
  position: absolute;
  padding: 24px;
  bottom: 0;
  left: 0;
  line-height: 20px;
  font-size: 12px;
  text-align: center;
  color: var(--td-text-color-placeholder);
  width: 100%;
  background: var(--td-bg-color-container);
}

.setting-drawer-container {
  .setting-container {
    padding-bottom: 100px;
  }

  .t-radio-group.t-size-m {
    min-height: 32px;

    width: 100%;
    height: auto;
    justify-content: space-between;
    align-items: center;
  }

  .setting-layout-drawer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 16px;

    .t-radio-button {
      display: inline-flex;
      height: 100%;
      max-height: 78px;
      padding: 8px;
      border-radius: var(--td-radius-default);
      border: 2px solid var(--td-component-border);
      height: auto;

      > .t-radio-button__label {
        display: inline-flex;
      }
    }

    .t-is-checked {
      border: 2px solid var(--td-brand-color) !important;
    }

    .t-form__controls-content {
      justify-content: end;
    }
  }

  .t-form__controls-content {
    justify-content: end;
  }
}

.setting-route-theme {
  .t-form__label {
    min-width: 310px !important;
    color: var(--td-text-color-secondary);
  }
}

.setting-color-theme {
  .setting-layout-drawer {
    .t-radio-button {
      height: 32px;
    }

    &:last-child {
      margin-right: 0;
    }
  }
}

.setting-drawer-container .t-radio-group.t-radio-group__outline.t-size-m .t-radio-button {
  height: auto;
}

// ==================== 现代化客服设置样式 ====================

// 现代化抽屉样式
.modern-drawer {
  :deep(.t-drawer__header) {
    background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%) !important;
    color: #1976D2 !important;
    font-weight: 700 !important;
    font-size: 1.2rem !important;
    border-bottom: 2px solid #e3f2fd !important;
    padding: 20px 24px !important;
  }

  :deep(.t-drawer__body) {
    background: linear-gradient(135deg, #f8fbff 0%, #ffffff 100%) !important;
    padding: 0 !important;
  }
}

// 现代化设置容器
.modern-setting-container {
  padding: 24px !important;
  padding-bottom: 120px !important;
  background: transparent !important;
}

// 联系方式区域
.contact-section {
  margin-bottom: 30px;

  .contact-card {
    display: flex;
    align-items: center;
    gap: 15px;
    background: #ffffff;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.08);
    border: 1px solid #e3f2fd;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.12);
      border-color: #bbdefb;
    }

    .contact-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: #1976D2;
      flex-shrink: 0;
    }

    .contact-content {
      flex: 1;

      .contact-label {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
        font-weight: 500;
      }

      .contact-value {
        font-size: 1.1rem;
        color: #1976D2;
        font-weight: 600;
      }
    }
  }
}

// 二维码区域
.qr-section {
  margin-bottom: 30px;

  .section-title {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 0 5px;

    .title-icon {
      font-size: 1.3rem;
      color: #1976D2;
    }

    .title-text {
      font-size: 1.1rem;
      font-weight: 600;
      color: #1976D2;
    }
  }

  .qr-container {
    background: #ffffff;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.08);
    border: 1px solid #e3f2fd;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.12);
      border-color: #bbdefb;
    }
  }

  .no-qr {
    text-align: center;
    padding: 30px 20px;
    color: #999;

    .no-qr-icon {
      font-size: 3rem;
      margin-bottom: 15px;
    }

    .no-qr-text {
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .qr-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    justify-items: center;
  }

  .qr-card {
    text-align: center;

    .qr-image {
      width: 150px;
      height: 150px;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
      border: 2px solid #e3f2fd;
      transition: all 0.3s ease;
      object-fit: cover;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 16px rgba(33, 150, 243, 0.25);
        border-color: #2196F3;
      }
    }

    .qr-label {
      margin-top: 10px;
      font-size: 0.9rem;
      color: #666;
      font-weight: 500;
    }
  }
}

// 帮助文档区域
.help-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, #f8fbff 0%, #e3f2fd 100%);
  border-top: 2px solid #e3f2fd;

  .help-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 24px;

    .help-icon {
      width: 50px;
      height: 50px;
      background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: white;
      flex-shrink: 0;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25);
    }

    .help-content {
      flex: 1;

      .help-title {
        font-size: 1rem;
        color: #1976D2;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .help-desc {
        font-size: 0.85rem;
        color: #666;
        font-weight: 400;
      }
    }

    .help-btn {
      display: flex;
      align-items: center;
      gap: 6px;
      border-radius: 10px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;
      border-color: #2196F3 !important;
      color: #2196F3 !important;

      &:hover {
        background: #2196F3 !important;
        color: white !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
      }

      .btn-icon {
        font-size: 0.9rem;
      }
    }
  }
}
</style>
