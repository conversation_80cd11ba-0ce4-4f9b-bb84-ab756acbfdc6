import { TOKEN_NAME } from '@/config/global';

const InitUserInfo = {
  roles: [],
};

// 定义的state初始值
const state = {
  userName:localStorage.getItem('starter-name') || '?',
  token: localStorage.getItem(TOKEN_NAME), // 默认token不走权限
  userInfo: InitUserInfo,
  site_img:localStorage.getItem('starter-site_img') || '',
  site_name:localStorage.getItem('starter-site_name') || '',
  admin:localStorage.getItem('starter-admin') || 0, // 默认token不走权限
};

const mutations = {
  setAdmin(state, admin) {
    localStorage.setItem('starter-admin', admin);
    state.admin = admin;
  },
  setUserName(state, name) {
    localStorage.setItem('starter-name', name);
    state.userName = name;
  },
  setToken(state, token) {
    localStorage.setItem(TOKEN_NAME, token);
    state.token = token;
  },
  removeToken(state) {
    localStorage.removeItem(TOKEN_NAME);
    state.token = '';
  },
  setUserInfo(state, userInfo) {
    state.userInfo = userInfo;
  },
};

const getters = {
  token: (state) => state.token,
  roles: (state) => state.userInfo?.roles,
};

const actions = {
  async login({ commit }, userInfo) {
    const mockLogin = async (userInfo) => {
      // 登录请求流程
      console.log(userInfo);
      // const { account, password } = userInfo;
      // if (account !== 'td') {
      //   return {
      //     code: 401,
      //     message: '账号不存在',
      //   };
      // }
      // if (['main_', 'dev_'].indexOf(password) === -1) {
      //   return {
      //     code: 401,
      //     message: '密码错误',
      //   };
      // }
      // const token = {
      //   main_: 'main_token',
      //   dev_: 'dev_token',
      // }[password];
      commit('setAdmin', userInfo.admin);
      commit('setUserName', userInfo.name);
      return userInfo;
    };

    const res = await mockLogin(userInfo);
    if (res.code === 200) {
      commit('setToken', res.token);
    } else {
      throw res;
    }
  },
  async getUserInfo({ commit, state }) {
    const mockRemoteUserInfo = async (token) => {
      console.log(token);
      if (parseInt(token) === 1) {
        return {
          name: 'td_main',
          roles: ['ALL_ROUTERS'],
        };
      }
      return {
        name: 'td_dev',
        roles: ['PlatformIndex','PlatformAdd','PlatformEdit','PlatformUpload', 'DashboardBase', 'login','DashboardEditPwd','CustomerInfo','CustomerLogInfo','CustomerAnnouncement'],
      };

    };
    console.log(state);
    const res = await mockRemoteUserInfo(state.admin);

    commit('setUserInfo', res);
  },
  async logout({ commit }) {
    commit('removeToken');
    commit('setUserInfo', InitUserInfo);
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
