import axios from 'axios';
import store from '../store'

// 使用开发环境API_HOST
let API_HOST = "/cloud/";
if (process.env.NODE_ENV === "production") {
  // 使用生产环境API_HOST
  API_HOST = window.location.protocol + "//" + window.location.hostname + "/web/index.php?s=/cloud/";
}

const CODE = {
  LOGIN_TIMEOUT: 1000,
  REQUEST_SUCCESS: 0,
  REQUEST_FOBID: 1001,
};

const instance = axios.create({
  baseURL: API_HOST,
  timeout: 500000,
  withCredentials: true,
});

// eslint-disable-next-line
// @ts-ignore
// axios的retry ts类型有问题
instance.interceptors.retry = 3;
// instance.interceptors.request.use((config) => config);
instance.interceptors.request.use((config) => {
  // Vue.ls本地储存token

  const token = store.state.user.token;
  if (token) {
    // 添加token请求头
    config.headers.token = token;
  }
  // config.headers.Locale = locale
  return config
}, error => Promise.reject(error))

instance.interceptors.response.use((response) => {
    if (response.status === 200) {
      const {data} = response;
      if (data.code == -1) {
        store.commit('user/removeToken');
      }
      return data;
    }
  },
  (err) => {
    const {config} = err;

    if (!config || !config.retry) return Promise.reject(err);

    config.retryCount = config.retryCount || 0;

    if (config.retryCount >= config.retry) {
      return Promise.reject(err);
    }

    config.retryCount += 1;

    const backoff = new Promise((resolve) => {
      setTimeout(() => {
        resolve({});
      }, config.retryDelay || 1);
    });

    return backoff.then(() => instance(config));
  },
);

export default instance;
