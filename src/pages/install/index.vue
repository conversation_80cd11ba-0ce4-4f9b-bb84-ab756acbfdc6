<template>
  <div class="list-card list-common-table">
    <t-card :bordered="false">
      <t-steps :defaultCurrent="current" :style="{ width: '900px',margin:'20px auto' }">
        <t-step-item title="榆落云安装协议"></t-step-item>
        <t-step-item title="安装环境监测"></t-step-item>
        <t-step-item title="系统数据设置"></t-step-item>
      </t-steps>
      <div style="margin-top: 40px">
        <template v-if="current==0">
          <t-card title="榆落云安装协议" :bordered="true" hover-shadow :style="{ width: '900px',margin:'0 auto' }">
            <div v-html="info"
                 style="height: 500px;    overflow-y: auto;white-space: break-spaces;line-height: 25px;"></div>
            <div style="text-align: center;padding: 20px">
              <t-checkbox v-model="ok">同意安装协议</t-checkbox>
            </div>
            <div style="text-align: center">
              <t-button @click="okIns">确定安装</t-button>
            </div>
          </t-card>
        </template>
        <template v-if="current==1">
          <t-card title="安装环境监测" :bordered="true" hover-shadow :style="{ width: '900px',margin:'0 auto' }">
            <div style="text-align: center;padding: 20px">
              <t-table
                row-key="index"
                :data="receiveCode"
                :columns="columns"
              >
                <template #value="{ row }">
                  <t-tag style="cursor: pointer" v-if="row.check==0" theme="danger">{{ row.value }}</t-tag>
                  <t-tag style="cursor: pointer" v-if="row.check==1" theme="success">{{ row.value }}</t-tag>
                </template>
              </t-table>
            </div>
            <div style="text-align: center">
              <t-space :style="{ width: '300px',margin:'0 auto',textAlign:'center' }">
                <t-button @click="getreceiveCode">重新检测</t-button>
                <t-button @click="okreceiveCode">下一步</t-button>
              </t-space>
            </div>
          </t-card>
        </template>
        <template v-if="current==2">
          <t-card title="数据库信息" :bordered="true" hover-shadow :style="{ width: '900px',margin:'0 auto' }">
            <t-form
              :data="formData"
              :rules="rules"
              @submit="onSubmit"
              :label-width="150"
            >
              <t-space direction="vertical" :size="40">
                <t-form-item label="数据库地址" name="database_ip">
                  <t-input :style="{ width: '480px' }" v-model="formData.database_ip"
                           placeholder="请填写数据库地址"></t-input>
                </t-form-item>
                <t-form-item label="数据库端口" name="database_no">
                  <t-input :style="{ width: '480px' }" v-model="formData.database_no"
                           placeholder="请填写数据库端口"></t-input>
                </t-form-item>
                <t-form-item label="数据库名称" name="database_name">
                  <t-input :style="{ width: '480px' }" v-model="formData.database_name"
                           placeholder="请填写数据库名称"></t-input>
                </t-form-item>
                <t-form-item label="数据库用户名" name="database_user_name">
                  <t-input :style="{ width: '480px' }" v-model="formData.database_user_name"
                           placeholder="请填写数据库用户名"></t-input>
                </t-form-item>
                <t-form-item label="数据库密码" name="database_pwd">
                  <t-input :style="{ width: '480px' }" v-model="formData.database_pwd"
                           placeholder="请填写数据库密码"></t-input>
                </t-form-item>
                <div style="font-weight: 600;font-size: 16px">创始人信息</div>
                <t-form-item label="管理员账号" name="admin_user_name">
                  <t-input :style="{ width: '480px' }" v-model="formData.admin_user_name"
                           placeholder="请填写管理员账号"></t-input>
                </t-form-item>
                <t-form-item label="管理员密码" name="admin_user_pwd">
                  <t-input :style="{ width: '480px' }" v-model="formData.admin_user_pwd"
                           placeholder="请填写管理员密码"></t-input>
                </t-form-item>
                <t-form-item label="重复密码" name="admin_user_pwd2">
                  <t-input :style="{ width: '480px' }" v-model="formData.admin_user_pwd2"
                           placeholder="请重复填写密码"></t-input>
                </t-form-item>
                <t-form-item label="二级密码"  help="升级框架、卸载应用的时候需要验证，请妥善保管" name="admin_two_pwd">
                  <t-input :style="{ width: '480px' }" v-model="formData.admin_two_pwd"
                           placeholder="请填写二级密码"></t-input>
                </t-form-item>
                <t-form-item>
                  <t-space :style="{ width: '300px',margin:'0 auto',textAlign:'center' }">
                    <t-button theme="primary" type="submit">创建数据</t-button>
                  </t-space>
                </t-form-item>
              </t-space>
            </t-form>
          </t-card>
        </template>
      </div>
    </t-card>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';

export default Vue.extend({
  data() {
    return {
      formData: {
        database_ip:'127.0.0.1',
        database_no:'3306'
      },
      current: 0,
      info: '',
      ok: false,
      receiveCode: [],
      receiveCount: 0,
      columns: [
        {colKey: 'name', title: '环境名称'},
        {colKey: 'value', title: '参数'},
        {colKey: 'msg', title: '系统要求'},
      ],
      rules: {
        database_ip: [{required: true, message: '请填写数据库地址', type: 'error'}],
        database_no: [{required: true, message: '请填写数据库端口', type: 'error'}],
        database_name: [{required: true, message: '请填写数据库名称', type: 'error'}],
        database_user_name: [{required: true, message: '请填写数据库用户名', type: 'error'}],
        database_pwd: [{required: true, message: '请填写数据库密码', type: 'error'}],
        admin_user_pwd2: [{required: true, message: '请填写管理员密码', type: 'error'}],
        admin_two_pwd: [{required: true, message: '请填写二级密码', type: 'error'}],
        admin_user_pwd: [{required: true, message: '请填写管理员密码', type: 'error'}],
        admin_user_name: [{required: true, message: '请填写管理员账号', type: 'error'}],

      }
    }
  },
  mounted() {
    this.getSeet();
  },
  methods: {
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Install/SetData",{form_data:this.formData})
          .then((res) => {
            if(res.code==200){
              this.$message.success(res.msg);
              setTimeout(()=>{
                this.$router.push('/login');
              },1500)
            }else{
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
            this.$message.error(e.message);
          });
      }

    },
    okIns() {
      if (!this.ok) {
        this.$message.error('请认真阅读且同意安装协议！');
        return;
      }
      this.current = 1;
      this.getreceiveCode();
    },
    okreceiveCode() {
      if (this.receiveCount > 0) {
        this.$message.error('系统检测未通过！');
        // return;
      }
      this.current = 2;
    },
    getSeet() {
      this.$request
        .post("/Install/agreement")
        .then((res) => {
          this.info = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getreceiveCode() {
      this.$request
        .post("/Install/receiveCode")
        .then((res) => {
          this.receiveCode = res.info;
          this.receiveCount = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },
  }
})
</script>
<style scoped lang="less">

</style>
