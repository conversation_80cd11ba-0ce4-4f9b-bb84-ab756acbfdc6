@import '@/style/variables.less';

// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}

.login-wrapper {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-size: cover;
  background-position: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
  }
}

.login-container {
  position: absolute;
  top: 50%;
  left: 5%;
  transform: translateY(-50%);
  min-height: 480px;
  padding: 40px;
  line-height: 22px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  box-shadow: 0 20px 40px rgba(33, 150, 243, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);

  @media (max-width: 768px) {
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    padding: 30px;
  }
}

.modern-login-container {
  // 现代化登录容器的额外样式
}

.title-container {
  text-align: center;
  margin-bottom: 40px;

  .title {
    font-size: 2rem;
    line-height: 1.3;
    color: var(--text-primary);
    margin-bottom: 12px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1);
    &.margin-no {
      margin-top: 0;
    }
  }

  .login-subtitle {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 400;
  }

  .sub-title {
    margin-top: 16px;

    .tip {
      display: inline-block;
      margin-right: 8px;
      font-size: 14px;

      &:first-child {
        color: var(--td-text-color-secondary);
      }

      &:last-child {
        color: var(--td-text-color-primary);
        cursor: pointer;
      }
    }
  }
}

// ==================== 现代化表单样式 ====================

// 现代化表单项
.modern-form-item {
  margin-bottom: 25px;
  width: 100%;
  .t-form__item-label {
    display: none; // 隐藏默认标签，使用浮动标签
  }

  // 确保所有表单项高度一致
  .t-form__controls {
    min-height: 56px;
    display: flex;
    align-items: stretch;
    width: 100%;
  }
}

// 浮动标签输入框
.floating-input {
  position: relative;
  width: 100%;
  .modern-input {
    width: 380px;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }

    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .floating-label {
    position: absolute !important;
    left: 6px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 验证码输入组
.verify-input-group {
  display: flex;
  gap: 15px;
  align-items: stretch; // 改为stretch确保高度一致

  .verify-input {
    flex: 1;

    .floating-input {
      height: 100%; // 确保高度填满

      .modern-input {
        height: 56px; // 明确指定高度
      }
    }
  }

  .verify-code-image {
    position: relative;
    cursor: pointer;
    border-radius: var(--radius-small);
    overflow: hidden;
    transition: var(--transition);
    height: 70px; // 与输入框高度一致
    display: flex;
    align-items: center;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
    }

    img {
      width: 130px;
      height: 56px; // 与输入框高度一致
      border-radius: var(--radius-small);
      border: 2px solid var(--border-color);
      transition: var(--transition);
      object-fit: cover;
      display: block;
    }

    .refresh-hint {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(33, 150, 243, 0.9);
      color: white;
      text-align: center;
      font-size: 0.75rem;
      padding: 2px;
      transform: translateY(100%);
      transition: var(--transition);
    }

    &:hover .refresh-hint {
      transform: translateY(0);
    }
  }
}

// 登录按钮
.btn-container {
  margin-top: 40px;

  .login-btn {
    height: 56px !important;
    border-radius: var(--radius-medium) !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0) !important;
    }

    .btn-content {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      .btn-icon {
        font-size: 1.2rem;
      }

      .btn-text {
        font-weight: 600;
      }
    }
  }
}

.item-container {
  width: 400px;
  margin-top: 30px;

  &.login-qrcode {
    .tip-container {
      width: 192px;
      margin-bottom: 16px;
      font-size: 14px;
      display: flex;
      justify-content: space-between;

      .tip {
        color: var(--td-text-color-primary);
      }

      .refresh {
        display: flex;
        align-items: center;
        color: var(--td-brand-color);

        .t-icon {
          font-size: 14px;
        }

        &:hover {
          cursor: pointer;
        }
      }
    }

    .bottom-container {
      margin-top: 32px;
    }
  }

  &.login-phone {
    .bottom-container {
      margin-top: 66px;
    }
  }

  .check-container {
    display: flex;
    align-items: center;

    &.remember-pwd {
      margin-bottom: 16px;
      justify-content: space-between;
    }

    .t-checkbox__label {
      color: var(--td-text-color-secondary);
    }

    span {
      color: var(--td-brand-color);

      &:hover {
        cursor: pointer;
      }
    }
  }

  .verification-code {
    display: flex;
    align-items: stretch; // 改为stretch确保高度一致

    .t-form__controls {
      width: 100%;
      display: flex;
      align-items: stretch;

      button {
        flex-shrink: 0;
        width: 102px;
        height: 56px; // 与输入框高度一致
        margin-left: 11px;
      }
    }

    // 兼容新的验证码布局
    &.modern-form-item {
      .t-form__controls {
        height: 56px;
      }
    }
  }

  .btn-container {
    margin-top: 48px;
  }
}

.switch-container {
  margin-top: 24px;

  .tip {
    font-size: 14px;
    color: var(--td-brand-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    margin-right: 14px;

    &:last-child {
      &::after {
        display: none;
      }
    }

    &::after {
      content: '';
      display: block;
      width: 1px;
      height: 12px;
      background: var(--td-gray-color-3);
      margin-left: 14px;
    }
  }
}

.check-container {
  font-size: 14px;
  color: var(--td-text-color-secondary);

  .tip {
    float: right;
    font-size: 14px;
    color: var(--td-brand-color);
  }
}

.copyright {
  font-size: 14px;
  position: absolute;
  left: 5%;
  bottom: var(--td-comp-size-xxxl);
  color: var(--td-text-color-secondary);
}

@media screen and (max-height: 700px) {
  .copyright {
    display: none;
  }
}

// ==================== 动画效果 ====================

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 表单项动画
.modern-form-item {
  animation: fadeIn 0.6s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.btn-container {
  animation: bounceIn 0.8s ease-out 0.4s both;
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
  height: 56px !important; // 统一所有输入框高度
  min-height: 56px !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }

  // 确保输入框内部元素对齐
  .t-input__inner {
    height: 52px !important; // 减去边框的高度
    line-height: 52px !important;
  }

  // 图标对齐
  .t-input__prefix,
  .t-input__suffix {
    height: 52px !important;
    display: flex !important;
    align-items: center !important;
  }
}

:deep(.t-button) {
  border-radius: 15px !important;
  transition: all 0.3s ease !important;

  &.t-button--theme-primary {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }
  }
}
