<template>
  <div class="login-wrapper" :style="{'background-image': 'url('+seet.login_bg+')' }">
    <div class="login-container modern-login-container">
      <div class="title-container">
        <h1 class="title margin-no">{{ seet.site_name }}</h1>
        <p class="login-subtitle">欢迎回来，请登录您的账户</p>
      </div>
      <t-form
        ref="form"
        :class="['item-container', `login-${type}`]"
        :data="formData"
        :rules="FORM_RULES"
        label-width="0"
        @submit="onSubmit"
      >
        <template>
          <t-form-item name="account" class="modern-form-item">
            <div class="floating-input">
              <t-input
                v-model="formData.account"
                size="large"
                placeholder="请输入账号"
                class="modern-input"
              >
                <template #prefix-icon>
                  <user-icon/>
                </template>
              </t-input>
              <label class="floating-label">账号</label>
            </div>
          </t-form-item>

          <t-form-item name="password" class="modern-form-item">
            <div class="floating-input">
              <t-input
                v-model="formData.password"
                size="large"
                :type="showPsw ? 'text' : 'password'"
                clearable
                key="password"
                placeholder="请输入登录密码"
                class="modern-input"
              >
                <template #prefix-icon>
                  <lock-on-icon/>
                </template>
                <template #suffix-icon>
                  <browse-icon v-if="showPsw" @click="showPsw = !showPsw" key="browse"/>
                  <browse-off-icon v-else @click="showPsw = !showPsw" key="browse-off"/>
                </template>
              </t-input>
              <label class="floating-label">密码</label>
            </div>
          </t-form-item>
          <t-form-item class="verification-code modern-form-item" name="verifyCode">
            <div class="verify-input-group">
              <div class="floating-input verify-input">
                <t-input
                  v-model="formData.verifyCode"
                  size="large"
                  placeholder="请输入验证码"
                  key="verifyCode"
                  class="modern-input"
                  style=" width: 220px;"
                >
                  <template #prefix-icon>
                    <t-icon name="shield-error"/>
                  </template>
                </t-input>
                <label class="floating-label">验证码</label>
              </div>
              <div class="verify-code-image" @click="refresh">
                <img id="captchaPic" :src="verify" alt="验证码">
                <div class="refresh-hint">点击刷新</div>
              </div>
            </div>
          </t-form-item>
        </template>
        <t-form-item class="btn-container">
          <t-button block size="large" type="submit" class="login-btn">
            <span class="btn-content">
              <span class="btn-text">登录</span>
            </span>
          </t-button>
        </t-form-item>
      </t-form>
    </div>
    <footer class="copyright" :style="`text-align: center;color: ${seet.text_color};`">
      <div>
        {{ seet.copyright }}
        <a target="_blank" :href="seet.record_url">{{ seet.record }}</a>
      </div>
    </footer>
  </div>
</template>
<script lang="ts">
import Vue from 'vue';
import {UserIcon, LockOnIcon, BrowseOffIcon, BrowseIcon} from 'tdesign-icons-vue';
import VueVerifyCode from 'vue-verify-code';

const INITIAL_DATA = {
  account: '',
  password: '',
  verifyCode: ''
};

const FORM_RULES = {
  account: [{required: true, message: '账号必填', type: 'error'}],
  password: [{required: true, message: '密码必填', type: 'error'}],
  verifyCode: [{required: true, message: '验证码必填', type: 'error'}],
};
/** 高级详情 */
export default Vue.extend({
  name: 'Login',
  components: {
    VueVerifyCode,
    UserIcon,
    LockOnIcon,
    BrowseOffIcon,
    BrowseIcon
  },
  data() {
    return {
      verify: './index.php?s=/cloud/Login/verify',
      forceRefresh: '',
      FORM_RULES,
      type: 'password',
      formData: {...INITIAL_DATA},
      showPsw: false,
      seet: {
        login_bg: '',
      }
    };
  },
  mounted() {
    this.getSeet();
  },
  methods: {
    refresh() {
      this.verify = this.verify + "&time=" + (new Date().getTime() / 1000);
    },
    getSeet() {
      this.$request
        .post("/login/Seet")
        .then((res) => {
          if (res.settings.exists == 0) {
            this.$router.push('/install');
          } else {
            this.seet = res.settings;
            localStorage.setItem('starter-site_img', res.settings.site_img);
            localStorage.setItem('starter-site_name', res.settings.site_name);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      // if (this.$refs.verifyCode.result != this.formData.verifyCode) {
      //   this.$message.error('验证码错误！');
      //   this.forceRefresh  = new Date().getTime()
      //   return;
      // }
      if (validateResult === true) {
        this.$request
          .post("/login/check_login", {
            account: this.formData.account,
            password: this.formData.password,
            verificationCode: this.formData.verifyCode
          })
          .then(async (res) => {
            if (res.code == 200) {
              await this.$store.dispatch('user/login', res);
              this.$message.success('登录成功');
              //this.$router.replace('/').catch(() => '');
              this.$router.push('/dashboard/base');
            } else {
              this.refresh();
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
  },
});
</script>
<style lang="less">
@import url('index.less');
:deep(.modern-form-item .t-form__controls-content){
  width: 100%;
}
</style>
