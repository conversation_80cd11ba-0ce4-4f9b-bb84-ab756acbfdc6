<template>
  <div class="modules-container">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <div class="filter-tabs">
        <div
          class="filter-tab"
          :class="{ active: currentFilter === 'all' }"
          @click="setFilter('all')"
        >
          <span class="tab-text">全部应用</span>
          <span class="tab-count">{{getTotalCount()}}</span>
        </div>
        <div
          class="filter-tab"
          :class="{ active: currentFilter === 'installed' }"
          @click="setFilter('installed')"
        >
          <span class="tab-text">已安装</span>
          <span class="tab-count">{{getInstalledCount()}}</span>
        </div>
        <div
          class="filter-tab"
          :class="{ active: currentFilter === 'uninstalled' }"
          @click="setFilter('uninstalled')"
        >
          <span class="tab-text">未安装</span>
          <span class="tab-count">{{getUninstalledCount()}}</span>
        </div>
      </div>
    </div>

    <!-- 应用列表 -->
    <div class="modules-content">
      <div class="modules-grid">
        <div
          v-for="(item,index) in filteredList"
          :key="item.id"
          class="module-card"
        >
          <div class="module-header">
            <div class="module-avatar">
              <img :src="item.icon" :alt="item.name" class="avatar-img">
            </div>
            <div class="module-actions" v-if="item.is_install==1">
              <t-dropdown :options="options" :min-column-width="112" @click="clickHandler($event,item)">
                <div class="action-trigger">
                  <span class="action-icon">•••</span>
                </div>
              </t-dropdown>
            </div>
          </div>

          <div class="module-content">
            <h3 class="module-title">{{item.name}}</h3>
            <p class="module-version">版本：{{item.version}}</p>

            <div class="module-type">
              <span class="type-text">{{item.mode_type}}</span>
            </div>
          </div>

          <div class="module-footer">
            <div class="module-status">
              <div v-if="item.is_install==1" class="status-badge installed">
                <span class="status-text">已安装</span>
              </div>
              <div v-else class="status-badge uninstalled" @click="insMode(item)">
                <span class="status-icon">+</span>
                <span class="status-text">点击安装</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框区域 -->
    <t-dialog
      :visible.sync="showMode"
      header="卸载提醒"
      confirmBtn="确定卸载"
      cancelBtn="关闭"
      :onConfirm="onConfirmDel"
    >
      <div style="padding-bottom: 20px;font-weight: 600;color: #d32f2f;text-align: center;font-size: 16px">警告：数据库内容不可恢复，数据库请做好备份！</div>
      <t-input-adornment prepend="二级密码">
        <t-input v-model="pass" type="password" placeholder="请输入二级密码"></t-input>
      </t-input-adornment>
    </t-dialog>

    <t-dialog
      :visible.sync="showEditMode"
      header="应用编辑"
      confirmBtn="保存"
      cancelBtn="关闭"
      :onConfirm="onConfirmModEdit"
    >
      <t-form
        :data="formData"
        ref="form"
        resetType="initial"
        colon
      >
        <t-form-item label="应用名称" name="name">
          <t-input v-model="formData.name" placeholder="请输入应用名称"></t-input>
        </t-form-item>
        <t-form-item label="应用图标" name="avatar">
          <t-upload
            action="/web/index.php?s=/cloud/Modules/UploadImg"
            :headers="{token:$store.state.user.token}"
            v-model="formData.avatar"
            theme="image"
            tips="请选择单张图片文件上传"
            accept="image/*"
          ></t-upload>
        </t-form-item>
      </t-form>
    </t-dialog>

    <t-dialog
      :visible="showUpdateMode"
      header="应用升级"
      :confirmBtn="{
        content: btnContent,
        theme: 'primary',
        loading,
      }"
      cancelBtn="取消"
      :onConfirm="getCheck"
      :onClose="onClose"
      :closeOnOverlayClick="false"
      :closeOnEscKeydown="false"
    >
      <t-row>
        <t-col :span="12">
          <t-space direction="vertical">
            <div>应用名称：{{showUpdateName}}</div>
            <div>服务到期时间：{{appInfo.expiryTime}}<span v-if="appInfo.expiryTimeCode==1" style="margin-left: 5px">{{appInfo.expiryTimeMsg}}</span></div>
            <div>需要框架版本：{{appInfo.frameVersion}}</div>
            <div>应用新版本号：{{appInfo.versionNum}}</div>
            <div>更新内容：</div>
          </t-space>
          <div style="white-space: break-spaces;" v-html="appInfo.updateContent"></div>
        </t-col>
      </t-row>
    </t-dialog>
  </div>
</template>
<script lang="ts">
import {
  HeartIcon, ChatIcon, ShareIcon, MoreIcon,
} from 'tdesign-icons-vue';
import { MessagePlugin } from 'tdesign-vue';

export default {
  name: 'ListCard',
  components: {
    HeartIcon,
    ChatIcon,
    ShareIcon,
    MoreIcon,
  },
  data() {
    return {
      formData:{
        name:'',
        avatar:[]
      },
      btnContent:'应用升级',
      loading: false,
      appInfo:{},
      showUpdateMode:false,
      showUpdateName:'',
      pass:'',
      delTypeSign:'',
      showMode:false,
      showEditMode:false,
      thisModInfo:'',
      info:{list:[]},
      currentFilter: 'all', // 当前筛选状态
      options: [
        {
          content: '编辑',
          value: 1,
        },
        {
          content: '卸载',
          value: 2,
        },
        {
          content: '检查更新',
          value: 3,
        },
      ],
    };
  },
  computed: {
    // 根据筛选条件过滤应用列表
    filteredList() {
      if (!this.info || !this.info.list) return [];

      switch (this.currentFilter) {
        case 'installed':
          return this.info.list.filter(item => item.is_install === 1);
        case 'uninstalled':
          return this.info.list.filter(item => item.is_install === 0);
        default:
          return this.info.list;
      }
    }
  },

  mounted() {
  this.getModeList();
  },
  methods: {
    // 设置筛选条件
    setFilter(filter) {
      this.currentFilter = filter;
    },
    // 获取总数量
    getTotalCount() {
      return this.info && this.info.list ? this.info.list.length : 0;
    },
    // 获取已安装数量
    getInstalledCount() {
      return this.info && this.info.list ? this.info.list.filter(item => item.is_install === 1).length : 0;
    },
    // 获取未安装数量
    getUninstalledCount() {
      return this.info && this.info.list ? this.info.list.filter(item => item.is_install === 0).length : 0;
    },
    getCheck(){
      if(this.appInfo.expiryTimeCode==1){
        this.$message.error('服务已到期，请续费后重试！');
       return;
      }
      var that=this;
      const confirmDia = this.$dialog.confirm({
        body: '你确定要升级吗?',
        confirmBtn: '确定升级',
        cancelBtn: '取消',
        onConfirm: ({ e }) => {
          this.loading=true;
          this.btnContent='升级中...';
          that.doUpdate();
          confirmDia.hide();
        },
        onClose: ({ e, trigger }) => {
          confirmDia.hide();
        },
      });
    },
    doUpdate(){
      this.$request
        .post("/Modules/updateAppDo",{id:this.thisModInfo.id,versionNum:this.appInfo.versionNum,frameVersion:this.appInfo.frameVersion})
        .then((res) => {
          if(res.code==200){
            this.onClose();
            this.getModeList();
            this.$message.success(res.msg);
          }else{
            this.$message.error(res.msg);
          }
          this.loading=false;
          this.btnContent='应用升级';
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onClose(){
      this.showUpdateMode=false;
    },
    getModeList(){
      this.$request
        .post("/Modules/index")
        .then((res) => {
          this.info=res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    insMode(item){
      MessagePlugin.loading('应用安装中...',0);
      this.$request
        .post("/Modules/InstallApp",{type_sign:item.type_sign,frame:item.frame_version,version:item.version})
        .then((res) => {
          MessagePlugin.closeAll();
          if(res.code==200){
            this.$message.success(res.msg);
            this.getModeList();
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onConfirmDel(){
      MessagePlugin.loading('应用卸载中...',0);
      this.$request
        .post("/Modules/UnInstallApp",{type_sign:this.delTypeSign,pass:this.pass})
        .then((res) => {
          MessagePlugin.closeAll();
          if(res.code==200){
            this.$message.success(res.msg);
            this.delTypeSign='';
            this.showMode=false;
            this.getModeList();
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onConfirmModEdit(){
      this.$request
        .post("/Modules/EditModule",{id:this.thisModInfo.id,app_name:this.formData.name,img_icon:this.formData.avatar[0].url})
        .then((res) => {
          if(res.code==200){
            this.$message.success(res.msg);
            this.showEditMode=false;
            this.formData.name='';
            this.formData.avatar=[];
            this.getModeList();
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    clickHandler(d,item) {
      console.log(item);
      this.thisModInfo=item;
      if(d.value==1){
        this.formData.name=item.name;
        this.formData.avatar=[{url:item.icon}];
        this.showEditMode=true;
      }else if(d.value==2){
        //删除
        this.delTypeSign=item.type_sign;
        this.showMode=true;
      }else{
        this.showUpdateName=item.name;
        this.$message.loading({ content: '应用更新检测中...', placement: 'top' ,duration: 0})
        this.checkUpadate(item.id);
      }
    },
    checkUpadate(id){
      this.$request
        .post("/Modules/updateapp",{id:id})
        .then((res) => {
          this.$message.closeAll();
          if(res.code==200){
            this.appInfo=res.info;
            if(res.info.isNew==0){
              this.$message.success('已经是最新版本了！');
            }else{
              this.showUpdateMode=true;
            }
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
      }
  }
};
</script>
<style scoped lang="less">
// ==================== 专业SaaS应用模块管理系统 ====================

// 页面容器
.modules-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 24px;
  position: relative;
}

// 页面标题区域
.page-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;

  .page-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #212529;
    margin: 0 0 8px 0;

    @media (max-width: 768px) {
      font-size: 1.5rem;
    }
  }

  .page-subtitle {
    font-size: 0.875rem;
    color: #6c757d;
    margin: 0;
    font-weight: 400;
  }
}

// 筛选区域
.filter-section {
  margin-bottom: 32px;
  position: relative;
  display: flex;
  justify-content: center;

  .filter-tabs {
    display: inline-flex;
    background: #ffffff;
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    gap: 2px;

    @media (max-width: 768px) {
      flex-wrap: wrap;
      gap: 4px;
      padding: 6px;
    }
  }

  .filter-tab {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: transparent;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    position: relative;
    white-space: nowrap;
    min-height: 40px;

    &:hover {
      background: #f8f9fa;
      transform: translateY(-1px);
    }

    &.active {
      background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
      color: white;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
      transform: translateY(-1px);

      .tab-text, .tab-count {
        color: white;
      }

      .tab-count {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
        border-radius: 8px;
        pointer-events: none;
      }
    }

    .tab-text {
      font-weight: 500;
      font-size: 0.875rem;
      letter-spacing: 0.025em;
    }

    .tab-count {
      background: #f1f3f4;
      color: #5f6368;
      padding: 3px 8px;
      border-radius: 12px;
      font-size: 0.75rem;
      font-weight: 600;
      min-width: 20px;
      text-align: center;
      line-height: 1.2;
      transition: all 0.3s ease;
      border: 1px solid transparent;
    }
  }
}

// 应用内容区域
.modules-content {
  position: relative;
}

// 应用网格
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
  }
}

// 应用卡片
.module-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e9ecef;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6 0%, #1e40af 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    border-color: #d1d5db;

    &::before {
      opacity: 1;
    }
  }
}

// 应用头部
.module-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  position: relative;

  .module-avatar {
    .avatar-img {
      width: 56px;
      height: 56px;
      border-radius: 12px;
      object-fit: cover;
      border: 2px solid #f1f3f4;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      transition: all 0.3s ease;
    }
  }

  .module-actions {
    .action-trigger {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      background: #f8f9fa;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border: 1px solid #e9ecef;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
        border-color: transparent;

        &::before {
          opacity: 1;
        }

        .action-icon {
          color: white;
          position: relative;
          z-index: 1;
        }
      }

      .action-icon {
        font-size: 14px;
        color: #6b7280;
        font-weight: bold;
        transition: color 0.3s ease;
        position: relative;
        z-index: 1;
      }
    }
  }
}

// 应用内容
.module-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .module-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
    line-height: 1.4;
    word-wrap: break-word;
    letter-spacing: -0.025em;
  }

  .module-version {
    font-size: 0.875rem;
    color: #9ca3af;
    margin: 0;
    font-weight: 500;
  }

  .module-type {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 12px;

    .type-text {
      font-size: 0.875rem;
      color: #6b7280;
      font-weight: 500;
      background: #f3f4f6;
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
    }
  }
}

// 应用底部
.module-footer {
  margin-top: auto;
  padding-top: 20px;

  .module-status {
    .status-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      padding: 12px 16px;
      border-radius: 12px;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      text-align: center;
      position: relative;
      overflow: hidden;

      &.installed {
        background: #f8fafc;
        color: #475569;
        border: 1px solid #e2e8f0;
        box-shadow: none;

        &:hover {
          background: #f1f5f9;
          border-color: #cbd5e1;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(71, 85, 105, 0.1);
        }
      }

      &.uninstalled {
        background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
        color: white;
        border: none;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);

        &:hover {
          background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
      }

      .status-icon {
        font-size: 16px;
        font-weight: bold;
      }

      .status-text {
        font-weight: 600;
        letter-spacing: 0.025em;
      }
    }
  }
}

// 对话框样式优化
:deep(.t-dialog) {
  .t-dialog__header {
    background: #f8f9fa;
    color: #212529;
    font-weight: 600;
    border-radius: 6px 6px 0 0;
    border-bottom: 1px solid #dee2e6;
  }

  .t-dialog__body {
    padding: 24px;
  }

  .t-button {
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.2s ease;

    &.t-button--theme-primary {
      background: #495057;
      border-color: #495057;

      &:hover {
        background: #343a40;
        border-color: #343a40;
      }
    }
  }
}

// 下拉菜单样式优化
:deep(.t-dropdown) {
  .t-dropdown__menu {
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #dee2e6;

    .t-dropdown__item {
      padding: 8px 12px;
      transition: all 0.2s ease;
      font-size: 0.875rem;

      &:hover {
        background: #f8f9fa;
        color: #495057;
      }
    }
  }
}

// 表单样式优化
:deep(.t-form-item) {
  .t-form__label {
    font-weight: 500;
    color: #495057;
  }
}

:deep(.t-input) {
  border-radius: 4px;

  &:focus {
    border-color: #495057;
    box-shadow: 0 0 0 2px rgba(73, 80, 87, 0.25);
  }
}

// 响应式优化
@media (max-width: 768px) {
  .modules-container {
    padding: 16px;
  }

  .module-card {
    height: auto;
    min-height: 200px;
  }

  .filter-section .filter-tabs {
    justify-content: center;
  }
}

// 兼容旧样式
.list-card-operation {
  display: flex;
  justify-content: space-between;

  .search-input {
    width: 360px;
  }
}

.list-card-items {
  margin: 14px 0 24px 0;
}
</style>
