<template>
  <div class="dashboard-container">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1 class="welcome-title">欢迎回来</h1>
        <p class="welcome-subtitle">这里是您的工作台概览</p>
      </div>
      <div class="quick-actions">
        <div class="action-item">
          <span class="action-label">今日</span>
          <span class="action-value">{{ new Date().toLocaleDateString() }}</span>
        </div>
      </div>
    </div>

    <div class="dashboard-content">
      <!-- 统计卡片区域 -->
      <t-row :gutter="[24, 24]" v-if="$store.state.user.admin == 1" class="stats-row">
        <t-col :xs="12" :xl="6">
          <div class="modern-stat-card user-stats">
            <div class="card-header">
              <div class="stat-icon">
                <div class="icon-bg">
                  <UsergroupIcon size="24" style="color: white !important;" />
                </div>
              </div>
              <div class="card-trend">
                <Filter2Icon size="16" class="trend-icon positive" />
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ info.user_count }}</div>
              <div class="stat-label">用户统计</div>
              <div class="stat-desc">当前用户记录数</div>
            </div>
            <div class="card-footer">
              <div class="progress-ring">
                <div class="ring-fill" style="--progress: 60%"></div>
              </div>
            </div>
          </div>
        </t-col>
        <t-col :xs="12" :xl="6">
          <div class="modern-stat-card platform-stats">
            <div class="card-header">
              <div class="stat-icon">
                <div class="icon-bg">
                  <ControlPlatformIcon  size="24" style="color: white !important;" />
                </div>
              </div>
              <div class="card-trend">
                <FillColor1Icon size="16" class="trend-icon positive" />
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ info.pl_count }}</div>
              <div class="stat-label">平台统计</div>
              <div class="stat-desc">当前平台总记录数</div>
            </div>
            <div class="card-footer">
              <div class="progress-ring">
                <div class="ring-fill" style="--progress: 75%"></div>
              </div>
            </div>
          </div>
        </t-col>
      </t-row>
      <t-row :gutter="[24, 24]" v-if="$store.state.user.admin == 0" class="stats-row">
        <t-col :xs="12" :xl="4">
          <div class="modern-stat-card total-quota">
            <div class="card-header">
              <div class="stat-icon">
                <div class="icon-bg">
                  <ChartIcon size="24" style="color: white !important;" />
                </div>
              </div>
              <div class="card-badge">
                <span class="badge-text">总计</span>
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ info.wechat_number + info.applet_number }}</div>
              <div class="stat-label">可创建平台数</div>
              <div class="stat-desc">当前可创建平台总数</div>
            </div>
            <div class="card-footer">
              <div class="progress-bar-modern">
                <div class="progress-fill" :style="{ width: '100%' }"></div>
              </div>
            </div>
          </div>
        </t-col>
        <t-col :xs="12" :xl="4">
          <div class="modern-stat-card used-quota">
            <div class="card-header">
              <div class="stat-icon">
                <div class="icon-bg">
                  <CheckCircleIcon size="24" style="color: white !important;" />
                </div>
              </div>
              <div class="card-badge success">
                <span class="badge-text">已用</span>
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ info.is_wechat + info.is_applet }}</div>
              <div class="stat-label">已创建平台数</div>
              <div class="stat-desc">当前已创建平台数</div>
            </div>
            <div class="card-footer">
              <div class="progress-bar-modern">
                <div class="progress-fill used" :style="{ width: getUsagePercentage() + '%' }"></div>
              </div>
            </div>
          </div>
        </t-col>
        <t-col :xs="12" :xl="4">
          <div class="modern-stat-card remaining-quota">
            <div class="card-header">
              <div class="stat-icon">
                <div class="icon-bg">
                  <HeartIcon size="24" />
                </div>
              </div>
              <div class="card-badge warning">
                <span class="badge-text">剩余</span>
              </div>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                {{ info.wechat_number + info.applet_number - (info.is_wechat + info.is_applet) }}
              </div>
              <div class="stat-label">剩余可创建数</div>
              <div class="stat-desc">当前剩余可创建平台数</div>
            </div>
            <div class="card-footer">
              <div class="progress-bar-modern">
                <div class="progress-fill remaining" :style="{ width: getRemainingPercentage() + '%' }"></div>
              </div>
            </div>
          </div>
        </t-col>
      </t-row>
      <!-- 系统公告区域 -->
      <div class="announcements-section" v-if="info.list && info.list.length > 0">
        <div class="section-header">
          <div class="header-content">
            <h3 class="section-title">
              <SoundIcon size="20" style="margin-right: 8px; color: #3b82f6;" />
              系统公告
            </h3>
            <p class="section-subtitle">最新动态与重要通知</p>
          </div>
          <div class="header-actions">
            <span class="view-all">查看全部</span>
          </div>
        </div>
        <div class="announcements-grid">
          <div
            v-for="item in info.list"
            :key="item.id"
            class="announcement-card-modern"
            @click="openUrl('/customer/info?id=' + item.id)"
          >
            <div class="card-header-modern">
              <div class="announcement-icon-modern">
                <div class="icon-bg-modern">
                  <FileIcon size="20" />
                </div>
              </div>
              <div class="card-status">
                <span class="status-dot"></span>
                <span class="status-text">最新</span>
              </div>
            </div>
            <div class="announcement-content-modern">
              <h4 class="announcement-title-modern">{{ item.title }}</h4>
              <div class="announcement-meta-modern">
                <span class="announcement-tag-modern">系统公告</span>
                <span class="announcement-date-modern">{{ item.add_time }}</span>
              </div>
            </div>
            <div class="card-footer-modern">
              <span class="read-more">阅读详情</span>
              <span class="arrow-icon">→</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  UsergroupIcon,
  ControlPlatformIcon,
  ChartIcon,
  CheckCircleIcon,
  HeartIcon,
  SoundIcon,
  FileIcon,
  ChevronRightIcon,
  FillColor1Icon,
  Filter2Icon
} from 'tdesign-icons-vue';

export default {
  name: 'DashboardBase',
  components: {
    UsergroupIcon,
    ControlPlatformIcon,
    ChartIcon,
    CheckCircleIcon,
    HeartIcon,
    SoundIcon,
    FileIcon,
    ChevronRightIcon,
    FillColor1Icon,
    Filter2Icon
  },
  data() {
    return {
      info: '',
    };
  },
  mounted() {
    this.getHome();
  },
  methods: {
    openUrl(url) {
      this.$router.push(url);
    },
    getHome() {
      this.$request
        .post('/index/my')
        .then((res) => {
          this.info = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getUsagePercentage() {
      if (!this.info) return 0;
      const total = this.info.wechat_number + this.info.applet_number;
      const used = this.info.is_wechat + this.info.is_applet;
      return total > 0 ? Math.round((used / total) * 100) : 0;
    },
    getRemainingPercentage() {
      if (!this.info) return 0;
      const total = this.info.wechat_number + this.info.applet_number;
      const remaining = total - (this.info.is_wechat + this.info.is_applet);
      return total > 0 ? Math.round((remaining / total) * 100) : 0;
    },
  },
};
</script>
<style scoped lang="less">
// ==================== 现代化仪表板设计系统 ====================

// CSS变量定义 - 经典蓝色系配色方案
:root {
  --primary-blue: #3b82f6;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-light: #60a5fa;
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  --gradient-secondary: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
  --gradient-accent: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
  --shadow-soft: 0 4px 20px rgba(59, 130, 246, 0.08);
  --shadow-medium: 0 8px 32px rgba(59, 130, 246, 0.12);
  --shadow-strong: 0 12px 40px rgba(59, 130, 246, 0.16);
  --border-radius-lg: 20px;
  --border-radius-md: 16px;
  --border-radius-sm: 12px;
}

// 页面容器
.dashboard-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ffffff 0%, #ffffff 100%);
  padding: 0;
  position: relative;

  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
  }
}

// 页面头部
.dashboard-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32px 24px;
  margin-bottom: 32px;
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.08), 0 4px 12px rgba(59, 130, 246, 0.04);
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    padding: 24px;
  }

  .welcome-section {
    .welcome-title {
      font-size: 2rem;
      font-weight: 700;
      background: var(--gradient-primary);
      -webkit-background-clip: text;
      background-clip: text;
      margin: 0 0 8px 0;
      line-height: 1.2;
      white-space: nowrap;
      letter-spacing: -0.02em;

      @media (max-width: 768px) {
        font-size: 1.75rem;
        white-space: normal;
      }
    }

    .welcome-subtitle {
      font-size: 1rem;
      color: #64748b;
      margin: 0;
      font-weight: 400;
    }
  }

  .quick-actions {
    display: flex;
    gap: 24px;

    .action-item {
      text-align: center;
      padding: 12px 20px;
      background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
      border-radius: var(--border-radius-sm);
      border: 1px solid rgba(59, 130, 246, 0.1);
      box-shadow: var(--shadow-soft);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-medium);
        border-color: rgba(59, 130, 246, 0.2);
      }

      .action-label {
        display: block;
        font-size: 0.75rem;
        color: #64748b;
        margin-bottom: 4px;
        font-weight: 500;
      }

      .action-value {
        display: block;
        font-size: 0.875rem;
        color: var(--primary-blue-dark);
        font-weight: 600;
      }
    }
  }
}

// 仪表板内容区域
.dashboard-content {
  position: relative;
  padding: 0 24px 32px;
}

// 统计行
.stats-row {
  margin-bottom: 40px;
}

// 现代化统计卡片
.modern-stat-card {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20px;
  padding: 28px;
  border: 1px solid #e2e8f0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  height: 220px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(30, 64, 175, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #cbd5e1;

    &::before {
      opacity: 1;
    }

    .stat-icon .icon-bg {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    }

    .card-footer-modern {
      .read-more {
        color: #1e40af;
      }

      .arrow-icon {
        color: #3b82f6;
        transform: translateX(4px);
      }
    }
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    z-index: 2;
    position: relative;

    .stat-icon {
      .icon-bg {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56px;
        height: 56px;
        border-radius: 16px;
        border: 2px solid #ffffff;
        transition: all 0.3s ease;

        :deep(.t-icon) {
          color: white !important;
          font-size: 24px;
        }

        :deep(svg) {
          color: white !important;
          fill: white !important;
        }

        :deep(svg path) {
          fill: white !important;
          stroke: white !important;
        }

        // 更强的选择器
        .icon-bg :deep(.t-icon svg) {
          color: white !important;
          fill: white !important;
        }

        .icon-bg :deep(.t-icon svg path) {
          fill: white !important;
        }
      }
    }
  }

  // 企业级专业配色 - 蓝色系渐变
  &:nth-child(1) {
    .stat-icon .icon-bg {
      background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
    }
  }

  &:nth-child(2) {
    .stat-icon .icon-bg {
      background: linear-gradient(135deg, #6366f1 0%, #4338ca 100%);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
    }
  }

  &:nth-child(3) {
    .stat-icon .icon-bg {
      background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
      box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
    }
  }

  &:nth-child(4) {
    .stat-icon .icon-bg {
      background: linear-gradient(135deg, #06b6d4 0%, #0e7490 100%);
      box-shadow: 0 4px 12px rgba(6, 182, 212, 0.2);
    }
  }

  &:nth-child(5) {
    .stat-icon .icon-bg {
      background: linear-gradient(135deg, #64748b 0%, #475569 100%);
      box-shadow: 0 4px 12px rgba(100, 116, 139, 0.2);
    }
  }

  .card-trend {
      .trend-indicator {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        font-size: 1rem;
        font-weight: 600;

        &.positive {
          background: linear-gradient(135deg, #10b981 0%, #059669 100%);
          color: white;
        }
      }

      .trend-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;

        &.positive {
          color: #000;
        }

        :deep(.t-icon) {
          color: white !important;
        }

        :deep(svg) {
          color: white !important;
          fill: white !important;
        }
      }
    }

    .card-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      background: #f1f5f9;
      color: #475569;
      border: 1px solid #e2e8f0;

      &.success {
        background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
        color: #166534;
        border-color: #86efac;
      }

      &.warning {
        background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        color: #92400e;
        border-color: #f59e0b;
      }
    }
  }

  .stat-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 2;
    position: relative;
  }

  .stat-number {
    font-size: 2.75rem;
    font-weight: 800;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 12px;
    line-height: 1;
    letter-spacing: -0.02em;
  }

  .stat-label {
    font-size: 1.125rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 6px;
  }

  .stat-desc {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 16px;
    line-height: 1.4;
  }

  .card-footer {
    z-index: 2;
    position: relative;

    .progress-ring {
      width: 40px;
      height: 40px;
      position: relative;

      .ring-fill {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background: conic-gradient(
          from 0deg,
          #3b82f6 0deg,
          #3b82f6 calc(var(--progress) * 3.6deg),
          #e2e8f0 calc(var(--progress) * 3.6deg),
          #e2e8f0 360deg
        );
        display: flex;
        align-items: center;
        justify-content: center;

        &::before {
          content: '';
          width: 28px;
          height: 28px;
          background: #ffffff;
          border-radius: 50%;
        }
      }
    }

    .progress-bar-modern {
      width: 100%;
      height: 6px;
      background: #f1f5f9;
      border-radius: 3px;
      overflow: hidden;
      position: relative;

      .progress-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
          animation: shimmer 2s infinite;
        }
      }
    }
  }

  .stat-trend {
    .trend-indicator {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      padding: 5px 10px;
      border-radius: 15px;
      font-size: 0.8rem;
      font-weight: 500;

      &.positive {
        background: #e8f5e8;
        color: #2e7d32;
      }

      .trend-icon {
        font-size: 1rem;
      }
    }
  }

  .stat-progress {
    margin-top: 12px;

    .progress-bar {
      width: 100%;
      height: 4px;
      background: #f1f3f4;
      border-radius: 2px;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: #64748b;
        border-radius: 2px;
        transition: width 0.6s ease;

        &.used {
          background: linear-gradient(90deg, #10b981 0%, #059669 100%);
        }

        &.remaining {
          background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
        }
      }
    }
  }

// 系统公告区域
.announcements-section {
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid #e2e8f0;
  position: relative;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }

    .header-content {
      .section-title {
        font-size: 1.75rem;
        font-weight: 700;
        color: #1e293b;
        margin: 0 0 8px 0;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .section-subtitle {
        font-size: 1rem;
        color: #64748b;
        margin: 0;
        font-weight: 400;
      }
    }

    .header-actions {
      .view-all {
        color: #3b82f6;
        font-size: 0.875rem;
        font-weight: 600;
        cursor: pointer;
        padding: 8px 16px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f9ff;
          color: #1e40af;
        }
      }
    }
  }

  .announcements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 24px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
}

// 现代化公告卡片
.announcement-card-modern {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(30, 64, 175, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
  }

  .card-header-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 2;
    position: relative;

    .announcement-icon-modern {
      .icon-bg-modern {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
        border-radius: 12px;
        border: 2px solid #ffffff;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);

        :deep(.t-icon) {
          color: #3b82f6;
          font-size: 20px;
        }
      }
    }

    .card-status {
      display: flex;
      align-items: center;
      gap: 6px;
      padding: 4px 12px;
      background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
      border-radius: 20px;
      border: 1px solid #86efac;

      .status-dot {
        width: 6px;
        height: 6px;
        background: #10b981;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      .status-text {
        font-size: 0.75rem;
        font-weight: 600;
        color: #166534;
      }
    }
  }

  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #cbd5e1;

    &::before {
      opacity: 1;
    }

    .announcement-arrow {
      transform: translateX(4px);
    }

    .card-footer-modern {
      .read-more {
        color: #1e40af;
      }

      .arrow-icon {
        color: #3b82f6;
        transform: translateX(4px);
      }
    }
  }

  .announcement-content-modern {
    flex: 1;
    z-index: 2;
    position: relative;

    .announcement-title-modern {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 12px;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .announcement-meta-modern {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 0.875rem;

      .announcement-tag-modern {
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        color: #475569;
        padding: 4px 12px;
        border-radius: 20px;
        font-weight: 500;
        border: 1px solid #cbd5e1;
        font-size: 0.75rem;
      }

      .announcement-date-modern {
        color: #64748b;
        font-weight: 400;
      }
    }
  }

  .card-footer-modern {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #f1f5f9;
    z-index: 2;
    position: relative;

    .read-more {
      font-size: 0.875rem;
      font-weight: 600;
      color: #3b82f6;
      transition: color 0.3s ease;
    }

    .arrow-icon {
      font-size: 1rem;
      color: #64748b;
      transition: all 0.3s ease;
    }


  }

  .announcement-date {
    color: #9ca3af;
  }

  .announcement-arrow {
    font-size: 1rem;
    color: #9ca3af;
    transition: transform 0.3s ease;
  }
}

// 动画效果
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 响应式优化
@media (max-width: 768px) {
  .dashboard-header {
    padding: 24px 16px;
  }

  .dashboard-content {
    padding: 0 16px 24px;
  }

  .modern-stat-card {
    height: auto;
    min-height: 200px;
    padding: 24px;

    .stat-number {
      font-size: 2.25rem;
    }

    .card-header .stat-icon .icon-bg {
      width: 48px;
      height: 48px;
      font-size: 1.25rem;
    }
  }

  .announcements-section {
    padding: 24px;

    .section-header {
      flex-direction: column;
      gap: 16px;
      text-align: center;
    }
  }

  .announcement-card-modern {
    padding: 20px;
  }
}

// 兼容旧样式
.row-container {
  margin-bottom: 16px;
}
</style>
