<template>
  <div class="announcement-detail-container">
    <!-- 导航区域 -->
    <div class="navigation-bar">
      <t-button
        @click="openUrl()"
        class="back-btn"
        size="large"
        theme="default"
      >
        <span class="back-icon">←</span>
        <span>返回</span>
      </t-button>
      <div class="breadcrumb">
        <span class="breadcrumb-item">数据统计</span>
        <span class="breadcrumb-separator">></span>
        <span class="breadcrumb-item current">公告详情</span>
      </div>
    </div>

    <!-- 公告内容区域 -->
    <div class="announcement-content">
      <!-- 公告头部 -->
      <div class="announcement-header">
        <div class="announcement-icon">
          <span class="icon-bg">公告</span>
        </div>
        <div class="announcement-meta">
          <h1 class="announcement-title">{{info.title}}</h1>
          <div class="announcement-info">
            <div class="info-item">
              <span class="info-label">发布时间</span>
              <span class="info-value">{{info.add_time}}</span>
            </div>
            <div class="info-item">

              <span class="info-value">系统公告</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 公告正文 -->
      <div class="announcement-body">
        <div class="content-wrapper" v-html="info.content"></div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  data() {
    return {
      info: '',
      id:'',
    };
  },

  mounted() {
    this.id = this.$route.query.id;
    this.getList();
  },
  methods: {
    openUrl(){
      this.$router.go(-1);
    },
    getList() {
      this.$request
        .post("/Settings/AboutInfo", {
          id: this.id,
        })
        .then((res) => {
          this.info = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
  }
};
</script>
<style scoped lang="less">
// ==================== 现代化公告详情页设计系统 ====================

// 页面容器
.announcement-detail-container {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 24px;
  position: relative;
}

// 导航栏
.navigation-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}

// 返回按钮
.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  color: #495057 !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04) !important;

  &:hover {
    background: #f8f9fa !important;
    border-color: #adb5bd !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08) !important;
  }

  .back-icon {
    font-size: 1rem;
    transition: transform 0.2s ease;
  }

  &:hover .back-icon {
    transform: translateX(-1px);
  }
}

// 面包屑导航
.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;

  .breadcrumb-item {
    color: #9ca3af;
    transition: color 0.2s ease;

    &.current {
      color: #374151;
      font-weight: 600;
    }
  }

  .breadcrumb-separator {
    color: #d1d5db;
    font-weight: 300;
  }
}

// 公告内容区域
.announcement-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e9ecef;
  overflow: hidden;
  position: relative;
}

// 公告头部
.announcement-header {
  background: #f8f9fa;
  padding: 32px;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  border-bottom: 1px solid #e9ecef;

  @media (max-width: 768px) {
    padding: 24px;
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .announcement-icon {
    .icon-bg {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
      border-radius: 12px;
      font-size: 0.875rem;
      font-weight: 600;
      color: white;
      flex-shrink: 0;

      @media (max-width: 768px) {
        width: 56px;
        height: 56px;
        font-size: 0.75rem;
      }
    }
  }

  .announcement-meta {
    flex: 1;
    min-width: 0;
  }

  .announcement-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 16px 0;
    line-height: 1.3;
    word-wrap: break-word;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 12px;
    }
  }

  .announcement-info {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;

    @media (max-width: 768px) {
      justify-content: center;
      gap: 16px;
    }

    .info-item {
      display: flex;
      align-items: center;
      gap: 6px;
      background: #ffffff;
      padding: 8px 12px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;

      .info-icon {
        font-size: 1rem;
      }

      .info-label {
        font-size: 0.875rem;
        color: #9ca3af;
        font-weight: 500;
      }

      .info-value {
        font-size: 0.875rem;
        color: #374151;
        font-weight: 600;
      }
    }
  }
}

// 公告正文
.announcement-body {
  padding: 40px;

  @media (max-width: 768px) {
    padding: 25px;
  }

  .content-wrapper {
    line-height: 1.8;
    font-size: 1rem;
    color: #333;

    // 优化HTML内容样式
    :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
      color: #1976D2;
      margin: 30px 0 15px 0;
      font-weight: 600;
      line-height: 1.4;

      &:first-child {
        margin-top: 0;
      }
    }

    :deep(h1) { font-size: 1.8rem; }
    :deep(h2) { font-size: 1.5rem; }
    :deep(h3) { font-size: 1.3rem; }
    :deep(h4) { font-size: 1.1rem; }

    :deep(p) {
      margin: 15px 0;
      text-align: justify;
      word-wrap: break-word;
    }

    :deep(ul), :deep(ol) {
      margin: 15px 0;
      padding-left: 25px;

      li {
        margin: 8px 0;
        line-height: 1.6;
      }
    }

    :deep(blockquote) {
      background: #f0f8ff;
      border-left: 4px solid #2196F3;
      padding: 15px 20px;
      margin: 20px 0;
      border-radius: 0 8px 8px 0;
      font-style: italic;
      color: #555;
    }

    :deep(code) {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      color: #e91e63;
    }

    :deep(pre) {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 8px;
      overflow-x: auto;
      margin: 15px 0;
      border: 1px solid #e0e0e0;

      code {
        background: none;
        padding: 0;
        color: #333;
      }
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
      border-radius: 8px;
      margin: 15px 0;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 20px 0;
      background: #ffffff;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      th, td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background: #f0f8ff;
        color: #1976D2;
        font-weight: 600;
      }

      tr:hover {
        background: #f8fbff;
      }
    }

    :deep(a) {
      color: #2196F3;
      text-decoration: none;
      border-bottom: 1px solid transparent;
      transition: all 0.3s ease;

      &:hover {
        border-bottom-color: #2196F3;
      }
    }

    :deep(strong), :deep(b) {
      color: #1976D2;
      font-weight: 600;
    }

    :deep(em), :deep(i) {
      color: #666;
      font-style: italic;
    }
  }
}

// 简化的动画效果（仅保留必要的交互反馈）

// 响应式优化
@media (max-width: 768px) {
  .announcement-detail-container {
    padding: 15px;
  }

  .navigation-bar {
    margin-bottom: 20px;
  }

  .announcement-header {
    .announcement-info {
      .info-item {
        flex: 1;
        min-width: 120px;
        justify-content: center;
      }
    }
  }
}

// 打印样式优化
@media print {
  .announcement-detail-container {
    background: white;
    padding: 0;

    &::before {
      display: none;
    }
  }

  .navigation-bar {
    display: none;
  }

  .announcement-content {
    box-shadow: none;
    border: none;
  }

  .announcement-header {
    background: white;
    border-bottom: 2px solid #e0e0e0;
  }
}
</style>
