<template>
  <div class="modern-form-container">
    <t-form
      :data="formData"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
        <!-- 功能选择标签页 -->
        <div class="tabs-section">
          <t-tabs v-model="formData.tab" :list="tabList" class="modern-tabs"/>
        </div>
        <!-- 表单内容区域 -->
        <div class="form-content">
          <template v-if="formData.tab==1">
            <!-- 修改密码表单 -->
            <div class="form-section">
              <div class="section-header">
                <h3 class="section-title">修改登录密码</h3>
                <p class="section-desc">为了账号安全，请定期更换密码</p>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.old_password"
                      type="password"
                      placeholder="请填写旧密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">旧密码</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.new_password"
                      type="password"
                      placeholder="请填写新密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">新密码</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.again_password"
                      type="password"
                      placeholder="请再次输入新密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">确认密码</label>
                  </div>
                </div>
              </div>

              <!-- 密码安全提示 -->
              <div class="security-tips">
                <div class="tips-title">
                  <span class="tips-icon">提示</span>
                  <span>密码安全建议</span>
                </div>
                <ul class="tips-list">
                  <li>密码长度至少8位，包含字母、数字和特殊字符</li>
                  <li>不要使用生日、姓名等容易被猜到的信息</li>
                  <li>定期更换密码，建议每3个月更换一次</li>
                  <li>不要在多个网站使用相同密码</li>
                </ul>
              </div>
            </div>
          </template>
          <template v-if="formData.tab==2">
            <!-- 修改账号表单 -->
            <div class="form-section">
              <div class="section-header">
                <h3 class="section-title">修改登录账号</h3>
                <p class="section-desc">修改账号需要验证二级密码</p>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.old_name"
                      type="text"
                      placeholder="请填写旧账号"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">旧账号</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.new_name"
                      type="text"
                      placeholder="请填写新账号"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">新账号</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.two_password"
                      type="password"
                      placeholder="请填写二级密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">二级密码</label>
                  </div>
                </div>
              </div>

              <!-- 账号修改提示 -->
              <div class="security-tips">
                <div class="tips-title">
                  <span class="tips-icon">提示</span>
                  <span>账号修改提示</span>
                </div>
                <ul class="tips-list">
                  <li>修改账号后需要使用新账号重新登录</li>
                  <li>账号修改后，原账号将无法登录系统</li>
                  <li>请确保记住新账号，避免无法登录</li>
                </ul>
              </div>
            </div>
          </template>
          <template v-if="formData.tab==3">
            <!-- 修改二级密码表单 -->
            <div class="form-section">
              <div class="section-header">
                <h3 class="section-title">修改二级密码</h3>
                <p class="section-desc">二级密码用于重要操作的安全验证</p>
              </div>

              <div class="form-grid">
                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.old_two_password"
                      type="password"
                      placeholder="请填写旧二级密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">旧二级密码</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.new_two_password"
                      type="password"
                      placeholder="请填写新二级密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">新二级密码</label>
                  </div>
                </div>

                <div class="form-group">
                  <div class="floating-input">
                    <t-input
                      v-model="formData.again_new_two_password"
                      type="password"
                      placeholder="请再次输入新二级密码"
                      size="large"
                      class="modern-input"
                    />
                    <label class="floating-label">确认二级密码</label>
                  </div>
                </div>
              </div>

              <!-- 二级密码提示 -->
              <div class="security-tips">
                <div class="tips-title">
                  <span class="tips-icon">提示</span>
                  <span>二级密码说明</span>
                </div>
                <ul class="tips-list">
                  <li>二级密码用于重要操作的安全验证</li>
                  <li>二级密码应与登录密码不同</li>
                  <li>请妥善保管二级密码，避免泄露</li>
                </ul>
              </div>
            </div>
          </template>

          <!-- 操作按钮 -->
          <div class="form-actions">
            <t-button
              theme="primary"
              type="submit"
              class="action-btn submit-btn"
              size="large"
            >
              <span>确认修改</span>
            </t-button>
          </div>
        </div>
    </t-form>
  </div>
</template>
<script lang="ts">
import store from "@/store";

export default {
  data() {
    return {

      tabList: [
        {
          label: '修改密码',
          value: '1',
        },
        {
          label: '修改帐号',
          value: '2',
        },
        {
          label: '修改二级密码',
          value: '3',
        },
      ],
      formData: {
        tab: '1',
        old_password: '',
        new_password: '',
        again_password: '',
        new_name: '',
        two_password: '',
        old_two_password: '',
        new_two_password: '',
        again_new_two_password: '',
        old_name: '',
      },
      rules: {
        old_password: [{required: true, message: '请填写旧密码', type: 'error'}],
        new_password: [{required: true, message: '请填写新密码', type: 'error'}],
        again_password: [{required: true, message: '请填写确认密码', type: 'error'}],
      },
    };
  },
  mounted() {

  },
  methods: {
    openUrl() {
      this.$router.go(-1);
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Index/user_password", this.formData)
          .then((res) => {
            if (res.code == 1) {
              this.$message.success('修改成功！');
              setTimeout(async () => {
                if (parseInt(this.formData.tab) !== 3) {
                  await store.commit('user/removeToken');
                  this.$router.push(`/login`);
                } else {
                  location.reload();
                }
              }, 1500);
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
  }
};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化账号安全页面设计系统 ====================

// 页面容器
.modern-form-container {
  min-height: 100vh !important;
  background: #f8f9fa;
  padding: 24px !important;
  position: relative !important;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 标签页区域
.tabs-section {
  margin-bottom: 32px;

  .modern-tabs {
    background: #ffffff;
    border-radius: 12px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e9ecef;
  }
}

// 表单内容区域
.form-content {
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  border-radius: 12px !important;
  padding: 32px !important;
  margin-bottom: 24px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04) !important;
  border: 1px solid #e9ecef !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    border-color: #d1d5db !important;
  }

  @media (max-width: 768px) {
    padding: 24px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 24px;
  text-align: center;

  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 6px 0;
  }

  .section-desc {
    color: #9ca3af;
    margin: 0;
    font-size: 0.875rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;
  margin-bottom: 30px;

  @media (max-width: 768px) {
    gap: 20px;
  }
}

// 表单组
.form-group {
  position: relative;
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input {
    width: 100%;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }
    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 安全提示区域
.security-tips {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;

  .tips-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;

    .tips-icon {
      font-size: 0.75rem;
      color: #6b7280;
      background: #e5e7eb;
      padding: 4px 6px;
      border-radius: 4px;
      font-weight: 600;
    }
  }

  .tips-list {
    margin: 0;
    padding-left: 16px;
    color: #6b7280;

    li {
      margin-bottom: 6px;
      font-size: 0.875rem;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25) !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 简化的动画效果（仅保留必要的交互反馈）

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-tabs) {
  .t-tabs__nav {
    background: transparent !important;
    border: none !important;
    gap: 2px !important;
  }

  .t-tabs__nav-item {
    border-radius: 8px !important;
    margin: 0 !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    font-size: 0.875rem !important;

    &:hover {
      background: #f8f9fa !important;
      color: #495057 !important;
    }

    &.t-is-active {
      background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%) !important;
      color: white !important;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.25) !important;
    }
  }

  .t-tabs__nav-item-wrapper {
    border: none !important;
  }

  .t-tabs__bar {
    display: none !important;
  }
}
</style>
