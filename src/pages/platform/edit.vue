<template>
  <div class="modern-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">编辑平台</h1>
    </div>
    <t-form
      :data="formData"
      @reset="onReset"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基础信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基础信息</h3>
          <p class="section-desc">设置平台的基本信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.app_name"
                placeholder="请填写公众号/小程序名称"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">公众号/小程序名称</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.desc"
                placeholder="请填写公众号/小程序描述"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">公众号/小程序描述</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.app_id"
                placeholder="请输入AppId"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">AppId</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.app_secret"
                placeholder="请输入AppSecret"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">AppSecret</label>
            </div>
          </div>
        </div>
      </div>
      <!-- 第二步：配置设置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">配置设置</h3>
          <p class="section-desc">设置平台的配置信息</p>
        </div>

        <div class="form-grid">
          <template v-if="$store.state.user.admin==1">
            <div class="form-group">
              <div class="floating-input">
                <t-select
                  v-model="formData.selectValue"
                  placeholder="请选择平台使用者"
                  size="large"
                  class="modern-select"
                >
                  <t-option
                    v-for="item in userList"
                    :value="item.id"
                    :label="item.name"
                    :key="item.id"
                  ></t-option>
                </t-select>
                <label class="floating-label">平台使用者</label>
              </div>
            </div>

            <div class="form-group">
              <div class="floating-input">
                <t-date-picker
                  :clearable="true"
                  placeholder="平台过期时间"
                  :enableTimePicker="true"
                  :allow-input="false"
                  v-model="formData.days"
                  size="large"
                  class="modern-datepicker"
                />
                <label class="floating-label">平台过期时间</label>
              </div>
            </div>
          </template>

          <div class="form-group" v-if="is==1">
            <div class="floating-input">
              <t-input
                v-model="formData.pigeno_url"
                placeholder="平台域名"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">平台域名</label>
            </div>
          </div>

          <div class="form-group full-width" style="padding-left: 15px;">
            <label class="floating-label">平台图标</label>
            <t-upload
              action="/web/index.php?s=/cloud/Modules/UploadImg"
              :headers="{token:$store.state.user.token}"
              v-model="formData.img_icon"
              theme="image"
              tips="请选择单张图片文件上传"
              accept="image/*"
              class="modern-upload"
            ></t-upload>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="default"
          @click="()=>{this.$router.push('/platform/index')}"
          class="action-btn back-btn"
          size="large"
        >
          <span>返回</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>保存修改</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {

  data() {
    return {
      formData: {
        days:'',
        selectValue: '',
        img_icon: [],
        app_name:'',
        app_id:'',
        app_secret:'',
        pigeno_url:'',
        desc:'',
      },
      showMode: false,
      list: [],
      userList: [],
      plaId: 0,
      is:0
    };
  },
  mounted() {
    this.plaId = this.$route.query.id;
    this.getUserList();
    this.getPlatformInfo();
    this.getUserIs();
  },
  methods: {
    getUserIs(){
      this.$request
        .post("/Platform/getUserIs")
        .then((res) => {
          this.is=res;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    dianType(d) {
      this.showMode = true;
      this.$request
        .post("/Platform/addPlatform", {type: d})
        .then((res) => {
          this.list = res.info;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getPlatformInfo() {
      this.$request
        .post("/Platform/getPlatformInfo", {id: this.plaId})
        .then((res) => {
          if (res.code == 200) {
            this.formData.selectValue=res.info.user_id;
            this.formData.app_name=res.info.platform_name;
            this.formData.desc=res.info.content;
            this.formData.app_id=res.info.app_id;
            this.formData.app_secret=res.info.app_secret;
            this.formData.days=res.info.overdue;
            this.formData.pigeno_url=res.info.pigeno_url;
            this.formData.img_icon=[{url:res.info.platform_img}];
          } else {

            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getUserList() {
      this.$request
        .post("/user/getUserMod",)
        .then((res) => {
          this.userList = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Platform/editPlatformFrom", {from_data: this.formData,id:this.plaId})
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg);
              setTimeout(() => {
                this.$router.push('index');
              }, 1500)
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
    modulesChange(d) {
      if (d.length == 0) {
        this.checkList = [];
      } else {
        var arr = [];
        d.forEach(i => {
          const result = this.list.find(item => item.value === i);
          arr.push(result);
        });
        this.checkList = arr;
      }
    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器 - 与platform/add.vue保持一致的蓝色主题
.modern-form-container {
  min-height: 100vh !important;
  background: #ffffff;
  padding: 40px 20px !important;
  position: relative !important;
  border-radius: 20px;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-bottom: 30px !important;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  @media (max-width: 768px) {
    padding: 25px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 30px;
  text-align: center;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.95rem;
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input,
  .modern-select,
  .modern-datepicker,
  .modern-textarea {
    width: 100%;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }
    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .modern-textarea {
    height: 120px;
    padding-top: 30px;
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 上传组件样式
.modern-upload {
  margin-top: 10px;
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      background: var(--primary-dark) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.back-btn {
    background: #f5f5f5 !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;

    &:hover {
      background: #eeeeee !important;
      color: #333 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
  position: relative !important;
  z-index: 1 !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
    z-index: 2 !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-select) {
  .t-input {
    border-radius: 10px !important;
    border: 2px solid transparent !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
    position: relative !important;
    z-index: 1 !important;

    &:focus {
      border-color: #2196F3 !important;
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
      z-index: 2 !important;
    }

    &:hover {
      border-color: #bbdefb !important;
    }
  }
}

:deep(.t-date-picker) {
  .t-input {
    border-radius: 10px !important;
    border: 2px solid transparent !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
    position: relative !important;
    z-index: 1 !important;

    &:focus {
      border-color: #2196F3 !important;
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
      z-index: 2 !important;
    }

    &:hover {
      border-color: #bbdefb !important;
    }
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: 10px !important;
    border: 2px dashed #e3f2fd !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: #2196F3 !important;
      background: #f8fbff !important;
    }
  }
}
</style>
