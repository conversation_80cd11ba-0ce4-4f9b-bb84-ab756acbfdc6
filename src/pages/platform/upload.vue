<template>
  <div class="modern-form-container">
    <t-form
      :data="formData"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：版本信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">版本信息</h3>
          <p class="section-desc">查看当前版本状态和配置新版本</p>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label class="modern-label">📦 上一次提交的版本号</label>
            <div class="version-display">
              <t-tag theme="primary" variant="light" class="version-tag">
                {{ plInfo.version == null ? '无版本记录' : plInfo.version }}
              </t-tag>
            </div>
          </div>

          <div class="form-group full-width">
            <div class="floating-input">
              <t-input
                v-model="formData.desc"
                placeholder="请输入版本描述（必填）"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">📝 版本描述</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：上传配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">上传配置</h3>
          <p class="section-desc">配置文件上传和密钥设置</p>
        </div>

        <div class="form-grid">
          <div class="form-group full-width" v-if="$store.state.user.admin==1">
            <label class="modern-label">📄 业务域名效验文件</label>
            <div class="upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Platform/UploadValidation"
                :data="{id:plInfo.id}"
                :autoUpload="true"
                theme="file"
                :multiple="false"
                :headers="{token:$store.state.user.token}"
                v-model="formData.file"
                class="modern-upload"
              />
              <t-tag theme="success" variant="light" class="upload-status">✅ 已上传</t-tag>
            </div>
          </div>

          <div class="form-group full-width">
            <label class="modern-label">🔑 小程序代码上传密钥</label>
            <div class="key-config">
              <t-tag
                v-if="!appealedShow"
                theme="primary"
                variant="light"
                @click="appealedShow = true"
                class="modify-btn"
              >
                ✏️ 点击修改
              </t-tag>
              <div v-if="appealedShow" class="floating-input">
                <t-textarea
                  v-model="formData.appealed"
                  placeholder="小程序代码上传密钥(不填写为不修改)"
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  class="modern-textarea"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          type="reset"
          theme="default"
          variant="base"
          class="action-btn reset-btn"
          size="large"
        >
          <span>重置</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>提交版本</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {

  data() {
    return {
      appealedShow:false,
      formData: {
        desc: '',
        appealed: '',
        file: [],
      },
      plInfo: {},
      plaId: 0,
    };
  },
  mounted() {
    this.plaId = this.$route.query.id;
    this.getInfo();
  },
  methods: {
    getInfo() {
      this.$request
        .post("/Platform/inside", {id: this.plaId})
        .then((res) => {
          this.plInfo = res.info;
          this.appealedShow=res.info.appealed==''?true:false;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/Platform/SubmitApp", {from_data: this.formData, id: this.plaId})
          .then((res) => {
            if (res.code == 1) {
              this.$message.success(res.msg);
              setTimeout(() => {
                this.$router.push('index');
              }, 1500)
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器
.modern-form-container {
  min-height: 100vh !important;
  background: #ffffff;
  padding: 40px 20px !important;
  position: relative !important;
  border-radius: 20px;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-bottom: 30px !important;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  @media (max-width: 768px) {
    padding: 25px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 30px;
  text-align: center;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.95rem;
}

// 版本显示区域
.version-display {
  padding: 15px 0;

  .version-tag {
    font-size: 1.1rem !important;
    padding: 8px 16px !important;
    border-radius: 12px !important;
    font-weight: 600 !important;
  }
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input,
  .modern-textarea {
    width: 100%;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }
  }

  .modern-input {
    height: 56px;
    padding: 20px 16px 8px 16px;

    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .modern-textarea {
    min-height: 120px;

    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 上传容器
.upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;

  .modern-upload {
    border-radius: 12px;
  }

  .upload-status {
    align-self: flex-start;
    font-weight: 600 !important;
    padding: 6px 12px !important;
    border-radius: 8px !important;
  }
}

// 密钥配置区域
.key-config {
  .modify-btn {
    cursor: pointer !important;
    font-weight: 600 !important;
    padding: 8px 16px !important;
    border-radius: 12px !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15) !important;
    }
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      background: var(--primary-dark) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.reset-btn {
    background: #f5f5f5 !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;

    &:hover {
      background: #eeeeee !important;
      color: #333 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-textarea) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: 12px !important;
    border: 2px dashed #e3f2fd !important;
    transition: all 0.3s ease !important;
    background: #f8fbff !important;

    &:hover {
      border-color: #2196F3 !important;
      background: #f0f8ff !important;
    }
  }

  .t-upload__file-list {
    margin-top: 15px;
  }
}

:deep(.t-tag) {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-1px) !important;
  }
}
</style>
