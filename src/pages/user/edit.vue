<template>
  <div class="modern-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">编辑用户</h1>
    </div>
    <t-form
      :data="formData"
      @submit="onSubmit"
      :label-width="0"
      class="modern-form"
    >
      <!-- 第一步：基础信息 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">基础信息</h3>
          <p class="section-desc">修改用户的基本账号信息</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.username"
                placeholder="请输入用户名，用户名为 3 到 20 个字符组成"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">用户名</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.password"
                type="password"
                placeholder="不填写不修改密码"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">新密码</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input
                v-model="formData.password_2"
                type="password"
                placeholder="重复输入密码"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">确认密码</label>
            </div>
          </div>

          <div class="form-group">
            <div class="switch-row">
              <label class="modern-label">允许修改密码</label>
              <t-switch size="large" v-model="formData.up_pwd"></t-switch>
            </div>
          </div>
        </div>
      </div>
      <!-- 第二步：应用配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">应用配置</h3>
          <p class="section-desc">选择用户可使用的应用模块</p>
        </div>

        <div class="form-grid">
          <div class="form-group full-width">
            <label class="modern-label">附加应用</label>
            <div class="app-selection">
              <div v-if="list.length>0" class="app-card-grid">
                <div
                  v-for="item in list"
                  :key="item.value"
                  class="app-card"
                  :class="{ active: formData.modules.includes(item.value) }"
                  @click="toggleApp(item.value)"
                >
                  <div class="app-card-content">
                    <div class="app-info">
                      <h4 class="app-name">{{ item.label }}</h4>
                      <p class="app-desc">应用模块</p>
                    </div>
                  </div>
                  <div class="app-check" :class="{ checked: formData.modules.includes(item.value) }">
                    <span class="check-icon">✓</span>
                  </div>
                </div>
              </div>
              <div v-if="list.length==0" class="no-apps">
                <div class="no-apps-icon">📭</div>
                <p>暂无可用应用</p>
              </div>
            </div>
          </div>

          <div class="form-group full-width">
            <label class="modern-label">应用创建数量</label>
            <div class="radio-card-group">
              <div class="radio-card" :class="{ active: formData.add_type === '0' }" @click="formData.add_type = '0'">
                <div class="radio-content">
                  <h4>可创建平台数量</h4>
                  <p>按平台类型设置创建数量</p>
                </div>
                <div class="radio-check" :class="{ checked: formData.add_type === '0' }">✓</div>
              </div>
              <div class="radio-card" :class="{ active: formData.add_type === '1' }" @click="formData.add_type = '1'">
                <div class="radio-content">
                  <h4>可创建应用数量</h4>
                  <p>按应用模块设置创建数量</p>
                </div>
                <div class="radio-check" :class="{ checked: formData.add_type === '1' }">✓</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 第三步：详细配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">详细配置</h3>
          <p class="section-desc">设置用户的详细权限和配置信息</p>
        </div>

        <div class="form-grid">
          <template v-if="formData.add_type=='0'">
            <div class="form-group">
              <div class="floating-input">
                <t-input
                  v-model="formData.wechat_number"
                  type="number"
                  placeholder="公众号平台创建数量"
                  size="large"
                  class="modern-input"
                />
                <label class="floating-label">公众号数量</label>
              </div>
            </div>

            <div class="form-group">
              <div class="floating-input">
                <t-input
                  v-model="formData.applet_number"
                  type="number"
                  placeholder="小程序平台创建数量"
                  size="large"
                  class="modern-input"
                />
                <label class="floating-label">小程序数量</label>
              </div>
            </div>
          </template>

          <template v-if="formData.add_type=='1'">
            <div class="form-group full-width">
              <div class="notice-card">
                <div class="notice-icon">⚠️</div>
                <div class="notice-text">编辑好后请前往用户列表增加权限</div>
              </div>
            </div>
          </template>

          <div class="form-group">
            <div class="floating-input">
              <t-date-picker
                :clearable="true"
                placeholder="选择账号过期时间(不填写为永不过期)"
                :enableTimePicker="true"
                :allow-input="false"
                v-model="formData.days"
                size="large"
                class="modern-datepicker"
              />
              <label class="floating-label">账号过期时间</label>
            </div>
          </div>

          <div class="form-group full-width">
            <div class="floating-input">
              <t-input
                v-model="formData.remark"
                placeholder="备注"
                size="large"
                class="modern-input"
              />
              <label class="floating-label">备注</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button
          theme="default"
          @click="openUrl()"
          class="action-btn back-btn"
          size="large"
        >
          <span>返回</span>
        </t-button>
        <t-button
          theme="primary"
          type="submit"
          class="action-btn submit-btn"
          size="large"
        >
          <span>保存修改</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
import {CheckCircleFilledIcon} from 'tdesign-icons-vue';

export default {
  components: {
    CheckCircleFilledIcon,
  },
  data() {
    return {
      formData: {
        add_type: '0',
        modules: [],
        wechat_number: 0,
        applet_number: 0,
        up_pwd: true,
        userId: '',
        username:'',
        days:'',
        remark:'',
      },
      checkList: [],
      list: [],
    };
  },
  mounted() {
    this.userId = this.$route.query.id;
    this.getList();
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      this.$request
        .post("/User/getUserInfo", {id: this.userId})
        .then((res) => {
          if (res.code == 200) {
            var numbers = res.info.pow_id.split(",");
            this.formData.modules = numbers.map(Number);
            this.formData.username = res.info.name;
            this.formData.add_type = res.info.pow.add_type + '';
            this.formData.days = res.info.pow.number;
            this.formData.wechat_number = res.info.pow.wechat_number;
            this.formData.applet_number = res.info.pow.applet_number;
            this.formData.up_pwd = res.info.pow.modify == 0 ? true : false;
            this.formData.remark = res.info.remarks;
            setTimeout(()=>{
              this.modulesChange(this.formData.modules);
            },100)

          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getList() {
      this.$request
        .post("/Modules/getModules",{key:1,user_id:this.userId})
        .then((res) => {
          this.list = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({validateResult}) {
      if (validateResult === true) {
        this.$request
          .post("/User/editUserDo", {id:this.userId,from_data: this.formData})
          .then((res) => {
            if (res.code == 200) {
              this.$message.success(res.msg);
              setTimeout(() => {
                this.$router.push('index');
              }, 1500)
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    openUrl() {
      this.$router.push('index');
    },
    toggleApp(value) {
      const index = this.formData.modules.indexOf(value);
      if (index > -1) {
        // 如果已选中，则移除
        this.formData.modules.splice(index, 1);
      } else {
        // 如果未选中，则添加
        this.formData.modules.push(value);
      }
      // 触发modulesChange
      this.modulesChange(this.formData.modules);
    },
    modulesChange(d) {
      if (d.length == 0) {
        this.checkList = [];
      } else {
        var arr = [];
        d.forEach(i => {
          const result = this.list.find(item => item.value === i);
          arr.push(result);
        });
        this.checkList = arr;
      }
    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器 - 与user/add.vue保持一致的蓝色主题
.modern-form-container {
  min-height: 100vh !important;
  background: #ffffff;
  padding: 40px 20px !important;
  position: relative !important;
  border-radius: 20px;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-bottom: 30px !important;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  @media (max-width: 768px) {
    padding: 25px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 30px;
  text-align: center;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
  font-size: 0.95rem;
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input,
  .modern-select,
  .modern-datepicker,
  .modern-textarea {
    width: 100%;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }
    &:not(:placeholder-shown) + .floating-label,
    &:focus + .floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .modern-textarea {
    height: 120px;
    padding-top: 30px;
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 开关行布局
.switch-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px;

  .modern-label {
    margin-bottom: 0;
    flex: 1;
  }
}

// 单选卡片组
.radio-card-group {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.radio-card {
  background: var(--background-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--radius-small);
  padding: 25px;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);

  &:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }

  &.active {
    border-color: var(--primary-color);
    background: var(--primary-light);
    box-shadow: var(--shadow-medium);
  }

  .radio-content {
    flex: 1;

    h4 {
      margin: 0 0 5px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    p {
      margin: 0;
      font-size: 0.9rem;
      color: var(--text-secondary);
    }
  }

  .radio-check {
    width: 24px;
    height: 24px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    color: transparent;
    transition: var(--transition);

    &.checked {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: white;
    }
  }
}

// 应用选择区域
.app-selection {
  margin-top: 15px;

  .app-card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
  }

  .no-apps {
    text-align: center;
    padding: 40px;
    color: var(--text-secondary);

    .no-apps-icon {
      font-size: 3rem;
      margin-bottom: 15px;
    }

    p {
      margin: 0;
      font-size: 1.1rem;
    }
  }
}

// 应用卡片
.app-card {
  background: #ffffff;
  border: 2px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 20px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.12);
  }

  &.active {
    border-color: var(--primary-color);
    background: var(--primary-light);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.12);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: var(--primary-color);
    }
  }

  .app-card-content {
    display: flex;
    align-items: center;
    gap: 15px;
    flex: 1;
  }

  .app-info {
    flex: 1;

    .app-name {
      margin: 0 0 5px 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .app-desc {
      margin: 0;
      font-size: 0.9rem;
      color: var(--text-secondary);
    }
  }

  .app-check {
    width: 28px;
    height: 28px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    background: #ffffff;

    .check-icon {
      font-size: 0.9rem;
      color: transparent;
      transition: var(--transition);
    }

    &.checked {
      background: var(--primary-color);
      border-color: var(--primary-color);
      box-shadow: 0 2px 8px rgba(33, 150, 243, 0.25);

      .check-icon {
        color: white;
      }
    }
  }

  // 选中状态的动画效果
  &.active .app-check {
    animation: checkBounce 0.3s ease-out;
  }
}

// 提醒卡片
.notice-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #fff3e0;
  border: 2px solid #ffcc02;
  border-radius: var(--radius-small);
  color: #f57c00;

  .notice-icon {
    font-size: 1.5rem;
  }

  .notice-text {
    font-weight: 600;
    font-size: 1rem;
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      background: var(--primary-dark) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }

  &.back-btn {
    background: #f5f5f5 !important;
    color: var(--text-secondary) !important;
    border: 1px solid var(--border-color) !important;

    &:hover {
      background: #eeeeee !important;
      color: #333 !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 选中动画
@keyframes checkBounce {
  0% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .radio-card-group {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
  position: relative !important;
  z-index: 1 !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
    z-index: 2 !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-date-picker) {
  .t-input {
    border-radius: 10px !important;
    border: 2px solid transparent !important;
    transition: all 0.3s ease !important;
    background: #ffffff !important;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;
    position: relative !important;
    z-index: 1 !important;

    &:focus {
      border-color: #2196F3 !important;
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
      z-index: 2 !important;
    }

    &:hover {
      border-color: #bbdefb !important;
    }
  }
}

:deep(.t-switch) {
  &.t-is-checked {
    background-color: #2196F3 !important;
  }
}
</style>
