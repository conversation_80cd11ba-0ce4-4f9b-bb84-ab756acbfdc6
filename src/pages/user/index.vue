<template>
  <div class="list-card list-common-table">
    <div class="list-card-items">
      <t-form ref="form" :data="formData" :label-width="80" @reset="onReset" @submit="onSubmit" layout="inline"
        :style="{ marginBottom: '30px' }">
        <t-button @click="openUrl('add')" style="margin-right: 20px" class="create-btn">
          新建用户
        </t-button>
        <t-form-item label="用户ID" name="user_id" :labelWidth="120">
          <t-input v-model="formData.user_id" class="form-item-content" type="search" placeholder="🔍 请输入用户ID"
            :style="{ minWidth: '84px' }" />
        </t-form-item>
        <t-form-item label="用户名称/备注" name="name" :labelWidth="120">
          <t-input v-model="formData.name" class="form-item-content" type="search" placeholder="🔍 请输入用户名称/备注"
            :style="{ minWidth: '84px' }" />
        </t-form-item>

        <t-form-item label="是否过期" name="expire" :labelWidth="120">
          <t-select v-model="formData.expire">
            <t-option label="📋 全部" value="0" />
            <t-option label="✅ 未过期" value="1" />
            <t-option label="❌ 已过期" value="2" />
          </t-select>
        </t-form-item>

        <t-button theme="primary" type="submit" :style="{ marginLeft: '8px' }" class="search-btn">查询</t-button>
        <t-button type="reset" variant="base" theme="default" class="reset-btn">重置</t-button>
      </t-form>
      <t-table rowKey="index" :data="list" :columns="columns" :stripe="true" :bordered="false" :hover="true"
        size="large" table-layout="auto" :showHeader="true" cellEmptyContent="-">
        <template #pwo_name="{ row }">
          <div v-for="item in row.pwo_name">
            <t-tag style="margin-bottom: 10px;" theme="primary">{{ item }}</t-tag>
          </div>
        </template>
        <template #number="{ row }">
          <div>公众号 ( {{ row.wechat_count }}<span style="margin:0px 5px">/</span>{{ row.wechat_number }} )</div>
          <div>小程序 ( {{ row.applet_count }}<span style="margin:0px 5px">/</span>{{ row.applet_number }} )</div>
        </template>
        <template #add_type="{ row }">
          <t-tag v-if="row.add_type == 0" theme="success">按总数创建</t-tag>
          <t-tag v-if="row.add_type == 1" theme="warning">分应用创建</t-tag>
        </template>
        <template #modify="{ row }">
          <t-tag style="cursor: pointer" v-if="row.modify == 0" @click="userModifyStauseUp(row)"
            theme="success">允许</t-tag>
          <t-tag style="cursor: pointer" v-if="row.modify == 1" @click="userModifyStauseUp(row)"
            theme="danger">禁止</t-tag>
        </template>
        <template #login_status="{ row }">
          <t-tag v-if="row.login_status == 0" theme="success">正常</t-tag>
          <t-tag style="cursor: pointer" @click="userLoginStauseUp(row)" v-if="row.login_status == 1"
            theme="danger">限制登录</t-tag>
        </template>
        <template #status="{ row }">
          <t-tag style="cursor: pointer" v-if="row.status == 0" theme="success" @click="userStauseUp(row)">正常</t-tag>
          <t-tag style="cursor: pointer" v-if="row.status == 1" theme="danger" @click="userStauseUp(row)">禁用</t-tag>
        </template>
        <template #op="{ row }">
          <a class="t-button-link" @click="openPower(row)" v-if="row.add_type == 1">权限</a>
          <a class="t-button-link" @click="openUrl('edit?id=' + row.id)">编辑</a>
        </template>
      </t-table>
      <div style="margin-top: 30px">
        <t-pagination :total="pagination.total" :page-size="pagination.size" @current-change="onCurrentChange"
          :showPageSize="false"></t-pagination>
      </div>

    </div>
    <t-dialog :visible.sync="showPowerMode" header="用户平台权限" :confirmBtn="null" cancelBtn="关闭" :width="800">
      <div style="padding: 20px 0px">
        <t-button @click="addPowerShow = true" class="create-btn">新增权限</t-button>
      </div>
      <div v-if="addPowerShow" style="padding-bottom: 20px">
        <t-form :data="formData" ref="form" @submit="onPowerSubmit" scrollToFirstError="smooth">
          <t-form-item label="选择应用" name="name">
            <t-radio-group v-model="modules" :options="Modlist">
            </t-radio-group>
          </t-form-item>
          <t-form-item label="过期时间" name="password">
            <t-date-picker placeholder="选择过期时间" :enableTimePicker="true" :allow-input="false"
              v-model="exp_time"></t-date-picker>
          </t-form-item>
          <t-form-item style="margin-left: 100px">
            <t-space size="10px">
              <t-button theme="primary" type="submit" class="search-btn">确定</t-button>
            </t-space>
          </t-form-item>
        </t-form>
      </div>
      <t-list :split="true">
        <t-list-item v-for="item in powerList" :key="item.id">
          <t-list-item-meta :image="item.mode_name.icon" :title="item.mode_name.name">
            <template #description>
              <span style="margin-right: 20px;">平台过期时间：{{ item.exp_time }}</span>
              <t-tag theme="warning" v-if="item.is_use == 0">未使用</t-tag>
              <t-tag theme="success" v-if="item.is_use == 1">已使用</t-tag>
            </template>
          </t-list-item-meta>
          <template #action>
            <t-popconfirm v-if="item.is_use == 0" theme="default" content="确认删除吗？" @confirm="delLimt(item)">
              <a class="t-button-link">删除</a>
            </t-popconfirm>
          </template>
        </t-list-item>
      </t-list>
    </t-dialog>
  </div>
</template>

<script lang="ts">
import { MessagePlugin } from 'tdesign-vue';
import {
  HeartIcon, ChatIcon, ShareIcon, MoreIcon,
} from 'tdesign-icons-vue';

export default {
  name: 'ListCard',
  components: {
    HeartIcon,
    ChatIcon,
    ShareIcon,
    MoreIcon,
  },
  data() {
    return {
      addPowerShow: false,
      exp_time: '',
      modules: '',
      Modlist: [],
      showPowerMode: false,
      powerInfo: {},
      powerList: [],
      total: 0,
      page: 1,
      formData: {
        name: '',
        expire: '',
        user_id: ''
      },
      list: [],
      columns: [
        { colKey: 'id', title: 'ID' },
        { colKey: 'name', title: '用户名' },
        { colKey: 'pwo_name', title: '应用权限' },
        { colKey: 'add_type', title: '创建类型' },
        { colKey: 'number', title: '已创建 / 总数量', width: '160px' },
        { colKey: 'add_time', title: '账号创建时间' },
        { colKey: 'days', title: '账号过期时间' },
        { colKey: 'remarks', title: '备注', ellipsisTitle: true, width: '200px' },
        { colKey: 'modify', title: '允许修改密码', align: 'center' },
        { colKey: 'login_status', title: '账号状态', align: 'center' },
        { colKey: 'status', title: '是否禁用', align: 'center' },
        { colKey: 'op', title: '操作', align: 'center', width: '150px' },
      ],
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
    };
  },

  mounted() {
    this.getList();
    this.getModList();
  },
  methods: {
    onCurrentChange(d) {
      this.pagination.page = d;
      this.getList();
    },
    delLimt(d) {
      this.$request
        .post("/User/delPowerLimit", { id: d.id })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
            this.openPowerList(d.user_id);
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onPowerSubmit() {
      if (this.modules == '') {
        this.$message.error('请选择应用');
        return;
      }
      if (this.exp_time == '') {
        this.$message.error('请选择过期时间');
        return;
      }
      this.$request
        .post("/User/insPowerLimit", { powerInfo: this.powerInfo, modules: this.modules, exp_time: this.exp_time })
        .then((res) => {
          if (res.code == 200) {
            this.exp_time = '';
            this.modules = '';
            this.$message.success(res.msg);
            this.openPowerList(this.powerInfo.id);
          } else {
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getModList() {
      this.$request
        .post("/Modules/getModules",)
        .then((res) => {
          this.Modlist = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openPowerList(id) {
      this.$request
        .post("/User/getUserPowerLimit", {
          user_id: id
        })
        .then((res) => {
          this.powerList = res.data;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    openPower(d) {
      this.showPowerMode = true;
      this.powerInfo = d;
      this.openPowerList(d.id);
    },
    getList() {
      this.$request
        .post("/User/getUser", {
          page: this.pagination.page,
          size: this.pagination.size,
          name: this.formData.name,
          user_id: this.formData.user_id,
          expire: this.formData.expire,
        })
        .then((res) => {
          this.list = res.data;
          this.pagination.total = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    userModifyStauseUp(item) {
      this.$request
        .post("/User/ModifyPwdStatus", {
          name: item.name
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg);
          }
          this.getList();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    userLoginStauseUp(item) {
      this.$request
        .post("/User/ModifyLoginStatus", {
          name: item.name
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg);
          }
          this.getList();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    userStauseUp(item) {
      this.$request
        .post("/User/ModifyStatus", {
          id: item.id,
          status: item.status
        })
        .then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
          } else {
            this.$message.error(res.msg);
          }
          this.getList();
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit() {
      this.pagination.page = 1;
      this.getList();
    },
    onReset() {
      this.page = 1;
      this.formData.search = '';
      this.formData.admin_name = '';
      this.getList();
    },
    openUrl(url) {
      this.$router.push(url);
    },
  }
};
</script>
<style scoped lang="less">
// 页面整体样式
.list-card {
  min-height: 100vh;

  .list-card-items {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
    backdrop-filter: blur(10px);
  }
}

// 表单区域样式
.t-form {
  background: #f0f8ff;
  padding: 20px;
  border-radius: 15px;
  margin-bottom: 30px !important;
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.06);

  .t-form-item {
    margin-bottom: 15px;
  }

  .form-item-content {
    border-radius: 10px;
    border: 2px solid transparent;
    transition: all 0.3s ease;

    &:focus {
      border-color: #0052d9;
      box-shadow: 0 0 0 3px rgba(0, 82, 217, 0.1);
    }
  }
}

// 按钮样式优化
.create-btn {
  background: #2196F3 !important;
  border: none !important;
  border-radius: 10px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

  &:hover {
    background: #1976D2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
  }
}

.search-btn {
  background: #2196F3 !important;
  border: none !important;
  border-radius: 10px !important;
  color: white !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

  &:hover {
    background: #1976D2 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
  }
}

.reset-btn {
  background: #f5f5f5 !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 10px !important;
  color: #666 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
  margin-left: 10px;

  &:hover {
    background: #eeeeee !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }
}

// 表格样式优化
.t-table {
  border-radius: 15px !important;
  overflow: hidden !important;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;

  .t-table__header {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%) !important;

    .t-table__header-cell {
      color: #1976D2 !important;
      font-weight: 600 !important;
      border-bottom: 2px solid #bbdefb !important;
    }
  }

  .t-table__body {
    .t-table__row {
      transition: all 0.3s ease;

      &:hover {
        background: #f8fbff !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(33, 150, 243, 0.08);
      }
    }
  }
}

// 标签样式优化
.t-tag {
  border-radius: 15px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15) !important;
  }
}

// 分页样式
.t-pagination {
  .t-pagination__total {
    color: #666;
    font-weight: 600;
  }

  .t-pagination__prev,
  .t-pagination__next,
  .t-pagination__item {
    border-radius: 10px;
    transition: all 0.3s ease;

    &:hover {
      background: #2196F3;
      color: white;
      transform: translateY(-1px);
    }

    &.t-is-current {
      background: #2196F3;
      color: white;
      box-shadow: 0 3px 8px rgba(33, 150, 243, 0.25);
    }
  }
}

// 对话框样式优化
.t-dialog {
  .t-dialog__header {
    background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
    color: #1976D2;
    font-weight: 600;
    border-radius: 15px 15px 0 0;
  }

  .t-dialog__body {
    padding: 30px;
  }
}

// 列表样式优化
.t-list {
  .t-list-item {
    border-radius: 10px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;

    &:hover {
      background: #f8fbff;
      border-color: #bbdefb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(33, 150, 243, 0.08);
    }
  }
}

// 响应式优化
@media (max-width: 768px) {
  .list-card {
    padding: 10px;

    .list-card-items {
      padding: 20px;
    }
  }

  .t-form {
    padding: 15px;
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.t-table {
  animation: fadeInUp 0.6s ease-out;
}

// 图标样式优化
.t-icon {
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.1);
  }
}
</style>
