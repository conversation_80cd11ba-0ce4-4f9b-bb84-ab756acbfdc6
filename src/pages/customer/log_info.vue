<template>
  <div class="modern-list-container">
    <div class="search-section" v-if="$store.state.user.admin==1">
      <div class="search-area">
        <div class="search-input-group">
          <t-input
            v-model="searchName"
            placeholder="请输入用户名称进行搜索..."
            size="large"
            class="search-input"
            @enter="searchList"
          >
            <template #prefix-icon>
              <span class="search-icon">👤</span>
            </template>
          </t-input>
          <t-button
            theme="primary"
            size="large"
            @click="searchList"
            class="search-btn"
          >
            <span class="btn-icon">🔍</span>
            <span>查询</span>
          </t-button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="modern-table-container">
        <t-table
          rowKey="index"
          :data="list"
          :columns="columns"
          :stripe="true"
          :bordered="false"
          :hover="true"
          size="large"
          table-layout="auto"
          :pagination="pagination"
          :showHeader="true"
          cellEmptyContent="-"
          class="modern-table"
        ></t-table>
      </div>
    </div>

    <!-- 分页区域 -->
    <div class="pagination-section">
      <t-pagination
        :total="pagination.total"
        :page-size="10"
        @current-change="onCurrentChange"
        :showPageSize="false"
        class="modern-pagination"
      ></t-pagination>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      list: [],
      searchName: '',
      pagination: {
        page: 1,
        size: 10,
        total: 0,
      },
      columns: [
        {colKey: 'user_name', title: '👤 用户信息'},
        {colKey: 'visit_ip', title: '🌐 用户IP'},
        {colKey: 'user_agent', title: '🖥️ 用户浏览器标识',width:'800px'},
        {colKey: 'annotate', title: '📝 说明'},
        {colKey: 'operating_time', title: '⏰ 访问时间'},
      ]
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    searchList() {
      this.pagination.page = 1;
      this.getList();
    },
    onCurrentChange(d) {
      this.pagination.page = d;
      this.getList();
    },
    getList() {
      this.$request
        .post("/Settings/log_list", {search: this.searchName, page: this.pagination.page, limit: this.pagination.size})
        .then((res) => {
          this.list = res.data;
          this.pagination.total = res.count;
        })
        .catch((e) => {
          console.log(e);
        });
    },

    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化列表页面设计系统 ====================

// 页面容器
.modern-list-container {
  min-height: 100vh !important;
  background:#ffffff;
  padding: 30px 20px !important;
  position: relative !important;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 搜索区域
.search-section {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
  border: 1px solid #e3f2fd;
  position: relative;
  z-index: 1;
}

.search-area {
  display: flex;
  align-items: center;

  .search-input-group {
    display: flex;
    gap: 15px;
    align-items: center;
    max-width: 500px;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 15px;
    }
  }

  .search-input {
    flex: 1;
    min-width: 300px;

    @media (max-width: 768px) {
      min-width: 100%;
    }
  }

  .search-icon {
    font-size: 1.2rem;
    color: #2196F3;
  }

  .search-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
    transition: all 0.3s ease !important;
    min-width: 100px;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    .btn-icon {
      font-size: 1.1rem;
    }
  }
}

// 表格区域
.table-section {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
  border: 1px solid #e3f2fd;
  position: relative;
  z-index: 1;
}

// 现代化表格容器
.modern-table-container {
  .modern-table {
    border-radius: 12px;
    overflow: hidden;
  }
}

// 分页区域
.pagination-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #ffffff;
  border-radius: 20px;
  padding: 20px 30px;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
  border: 1px solid #e3f2fd;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .pagination-info {
    .info-text {
      color: #666;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }

  .modern-pagination {
    display: flex;
    align-items: center;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.search-section {
  animation: slideInUp 0.6s ease-out 0.1s both;
}

.table-section {
  animation: slideInUp 0.6s ease-out 0.2s both;
}

.pagination-section {
  animation: slideInUp 0.6s ease-out 0.3s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-list-container {
    padding: 20px 10px;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-table) {
  border-radius: 12px !important;
  overflow: hidden !important;

  .t-table__header {
    background: #f8fbff !important;

    th {
      background: #f8fbff !important;
      color: #1976D2 !important;
      font-weight: 600 !important;
      border-bottom: 2px solid #e3f2fd !important;
    }
  }

  .t-table__body {
    tr {
      transition: all 0.3s ease !important;

      &:hover {
        background: #f0f8ff !important;
        transform: scale(1.01) !important;
      }

      &.t-table__row--striped {
        background: #fafbff !important;

        &:hover {
          background: #f0f8ff !important;
        }
      }
    }

    td {
      border-bottom: 1px solid #f0f0f0 !important;
      padding: 16px 12px !important;
    }
  }
}

:deep(.t-pagination) {
  .t-pagination__btn {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;

    &:hover {
      background: #2196F3 !important;
      color: white !important;
      transform: translateY(-1px) !important;
    }

    &.t-is-current {
      background: #2196F3 !important;
      color: white !important;
      box-shadow: 0 4px 8px rgba(33, 150, 243, 0.25) !important;
    }
  }
}
</style>
