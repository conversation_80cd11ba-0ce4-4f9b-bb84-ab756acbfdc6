<template>
  <div class="modern-list-container">
    <!-- 统计区域 -->
    <div class="stats-section">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">{{list.length}}</div>
          <div class="stat-label">检查项目</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{normalCount}}</div>
          <div class="stat-label">正常项目</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">⚠️</div>
        <div class="stat-content">
          <div class="stat-number">{{warningCount}}</div>
          <div class="stat-label">需要调整</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🔄</div>
        <div class="stat-content">
          <div class="stat-number">{{upgradeCount}}</div>
          <div class="stat-label">需要升级</div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <div class="modern-table-container">
        <t-table
          rowKey="index"
          :data="list"
          :columns="columns"
          :stripe="true"
          :bordered="false"
          :hover="true"
          size="large"
          table-layout="auto"
          :showHeader="true"
          cellEmptyContent="-"
          class="modern-table"
        >
        <template #check="{ row }">
          <t-tag v-if="row.check==1" theme="success">正常</t-tag>
          <t-tag v-if="row.check==0" theme="warning">建议调整</t-tag>
          <t-tag v-if="row.check==3" theme="primary" style="cursor: pointer" @click="showMode = true">升级</t-tag>
        </template>
        </t-table>
      </div>
    </div>

    <t-dialog
      :visible="showMode"
      :header="!showUpdata?'权限验证':'版本文件上传'"
      confirmBtn="确定"
      cancelBtn="取消"
      :onConfirm="getCheck"
      :onClose="onClose"
      :closeOnOverlayClick="false"
      :closeOnEscKeydown="false"
    >
      <t-input-adornment prepend="二级密码" v-if="!showUpdata">
        <t-input v-model="pass" type="password" placeholder="请输入二级密码"></t-input>
      </t-input-adornment>
      <t-row v-if="showUpdata">
        <t-col :span="6" :offset="4">
          <t-upload style="margin: 0 auto"
                    action="/web/index.php?s=/cloud/Modules/UploadZip"
                    :headers="{token:$store.state.user.token}"
                    v-model="newFile"
                    theme="file"
                    accept=".zip"
                    :onSuccess="get_file"
          ></t-upload>
        </t-col>
      </t-row>
    </t-dialog>
  </div>

</template>
<script>
export default {
  data() {
    return {
      showMode: false,
      showUpdata: false,
      newFile: [],
      pass: '',
      list: [],
      columns: [
        {colKey: 'name', title: '🖥️ 系统信息', align: 'center'},
        {colKey: 'value', title: '⚙️ 参数', align: 'center'},
        {colKey: 'check', title: '📊 当前状态', align: 'center'},
        {colKey: 'msg', title: '💡 建议', align: 'center'},
      ]
    };
  },
  computed: {
    normalCount() {
      return this.list.filter(item => item.check === 1).length;
    },
    warningCount() {
      return this.list.filter(item => item.check === 0).length;
    },
    upgradeCount() {
      return this.list.filter(item => item.check === 3).length;
    }
  },
  mounted() {
    this.getList();
  },
  methods: {
    get_file(d) {
      var res = d.response;
      if (res.code == 200) {
        this.$message.success(res.msg);
      } else {
        this.$message.error(res.msg);
      }
      setTimeout(() => {
        this.showMode = false;
        this.showUpdata = false;
        window.location.reload();
      }, 1500)
    },
    onClose() {
      this.showMode = false;
    },
    getCheck() {
      if (this.showUpdata) {
        this.showMode = false;
        this.showUpdata = false;
        this.getList();
      } else {
        this.$request
          .post("/Settings/checkLimi", {pass: this.pass})
          .then((res) => {
            console.log(res);
            if (res.code == 200) {
              this.showUpdata = true;
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    getList() {
      this.$request
        .post("/Settings/check_info")
        .then((res) => {
          this.list = res.list;
        })
        .catch((e) => {
          console.log(e);
        });
    },

    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化系统信息页面设计系统 ====================

// 页面容器
.modern-list-container {
  min-height: 100vh !important;
  background:#ffffff;
  padding: 30px 20px !important;
  position: relative !important;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 统计区域
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

// 统计卡片
.stat-card {
  background: #ffffff;
  border-radius: 20px;
  padding: 25px;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
  border: 1px solid #e3f2fd;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 20px;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.15);
    border-color: #bbdefb;
  }

  .stat-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f0f8ff 0%, #e3f2fd 100%);
    border-radius: 50%;
    border: 2px solid #e3f2fd;
  }

  .stat-content {
    flex: 1;

    .stat-number {
      font-size: 2rem;
      font-weight: 700;
      color: #1976D2;
      margin-bottom: 5px;
      line-height: 1;
    }

    .stat-label {
      font-size: 0.9rem;
      color: #666;
      font-weight: 500;
    }
  }

  // 不同类型的统计卡片颜色
  &:nth-child(1) {
    .stat-icon {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      color: #1976D2;
    }
  }

  &:nth-child(2) {
    .stat-icon {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
      color: #388E3C;
    }
  }

  &:nth-child(3) {
    .stat-icon {
      background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
      color: #f57c00;
    }
  }

  &:nth-child(4) {
    .stat-icon {
      background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 100%);
      color: #7b1fa2;
    }
  }
}

// 表格区域
.table-section {
  background: #ffffff;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08);
  border: 1px solid #e3f2fd;
  position: relative;
  z-index: 1;
}

// 现代化表格容器
.modern-table-container {
  .modern-table {
    border-radius: 12px;
    overflow: hidden;
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

.stat-card {
  animation: bounceIn 0.6s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
  &:nth-child(4) { animation-delay: 0.4s; }
}

.table-section {
  animation: slideInUp 0.6s ease-out 0.5s both;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-list-container {
    padding: 20px 10px;
  }
}

// TDesign组件样式覆盖
:deep(.t-table) {
  border-radius: 12px !important;
  overflow: hidden !important;

  .t-table__header {
    background: #f8fbff !important;

    th {
      background: #f8fbff !important;
      color: #1976D2 !important;
      font-weight: 600 !important;
      border-bottom: 2px solid #e3f2fd !important;
    }
  }

  .t-table__body {
    tr {
      transition: all 0.3s ease !important;

      &:hover {
        background: #f0f8ff !important;
        transform: scale(1.01) !important;
      }

      &.t-table__row--striped {
        background: #fafbff !important;

        &:hover {
          background: #f0f8ff !important;
        }
      }
    }

    td {
      border-bottom: 1px solid #f0f0f0 !important;
      padding: 16px 12px !important;
    }
  }
}

// 状态标签样式优化
:deep(.t-tag) {
  border-radius: 20px !important;
  padding: 6px 16px !important;
  font-weight: 600 !important;
  font-size: 0.85rem !important;
  border: none !important;
  transition: all 0.3s ease !important;

  &.t-tag--success {
    background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.25) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(76, 175, 80, 0.35) !important;
    }
  }

  &.t-tag--warning {
    background: linear-gradient(135deg, #f57c00 0%, #ef6c00 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(245, 124, 0, 0.25) !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(245, 124, 0, 0.35) !important;
    }
  }

  &.t-tag--primary {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%) !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;
    cursor: pointer !important;

    &:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }
  }
}

// 弹窗样式优化
:deep(.t-dialog) {
  .t-dialog__header {
    background: #f8fbff !important;
    color: #1976D2 !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #e3f2fd !important;
  }

  .t-dialog__body {
    padding: 30px !important;
  }

  .t-dialog__footer {
    background: #f8fbff !important;
    border-top: 1px solid #e3f2fd !important;
    padding: 20px 30px !important;

    .t-button {
      border-radius: 10px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;

      &:hover {
        transform: translateY(-2px) !important;
      }
    }
  }
}

// 输入框样式优化
:deep(.t-input-adornment) {
  border-radius: 10px !important;
  overflow: hidden !important;

  .t-input-adornment__prepend {
    background: #f8fbff !important;
    color: #1976D2 !important;
    font-weight: 600 !important;
    border-right: 2px solid #e3f2fd !important;
  }

  .t-input {
    border: none !important;
    background: #ffffff !important;

    &:focus {
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
    }
  }
}

// 上传组件样式优化
:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: 12px !important;
    border: 2px dashed #e3f2fd !important;
    background: #f8fbff !important;
    transition: all 0.3s ease !important;

    &:hover {
      border-color: #2196F3 !important;
      background: #f0f8ff !important;
    }
  }
}
</style>
