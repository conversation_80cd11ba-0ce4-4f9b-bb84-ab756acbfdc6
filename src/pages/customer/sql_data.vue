

<template>
  <t-card :bordered="false">
    <div>
      <div style="padding: 15px;font-weight: 600;font-size: 20px;color: #FF0000">操作时请咨询客服，操作请备份好数据库！</div>
      <t-form style="margin-top: 30px" v-if="!showMode"
        @submit="onSubmit"
      >
        <t-space direction="vertical" :size="40">
          <t-form-item label="请选择应用" name="username">
            <t-select  :style="{ width: '480px' }" v-model="type_sign">
              <template v-for="item in list">
                <t-option key="apple" :label="item.name" :value="item.type_sign" ></t-option>
              </template>
            </t-select>
          </t-form-item>
          <t-form-item>
            <t-space>
              <t-button theme="primary" type="submit">确定</t-button>
            </t-space>
          </t-form-item>
        </t-space>
      </t-form>
    </div>
    <t-dialog
      :visible="showMode"
      header="权限验证"
      confirmBtn="验证"
      cancelBtn="取消"
      :onConfirm="getCheck"
      :onClose="onClose"
      :closeOnOverlayClick="false"
      :closeOnEscKeydown="false"
    >
      <t-input-adornment prepend="二级密码">
        <t-input v-model="pass" type="password" placeholder="请输入二级密码"></t-input>
      </t-input-adornment>
    </t-dialog>
  </t-card>
</template>
<script lang="ts">
export default {
  data() {
    return {
      showMode:true,
      pass:'',
      list:[],
      type_sign:''
    }
  },
  mounted() {
  },
  methods:{
    onSubmit(){
      this.$request
        .post("/Settings/sql_data",{type_sign:this.type_sign})
        .then((res) => {
          if(res.code==200){
            this.$message.success(res.msg);
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    getCheck(){
      this.$request
        .post("/Settings/checkLimi",{pass:this.pass})
        .then((res) => {
          if(res.code==200){
            this.showMode=false;
            this.list=res.info;
          }else{
            this.$message.error(res.msg);
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onClose(){
      this.$router.push('/dashboard/base');
    },
  },
}
</script>
<style scoped lang="less">

</style>
