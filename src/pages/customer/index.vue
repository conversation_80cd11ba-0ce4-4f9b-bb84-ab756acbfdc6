<template>
  <div class="modern-form-container">
    <t-form :data="formData" @reset="onReset" @submit="onSubmit" :label-width="0" class="modern-form">
      <!-- 第一步：联系方式 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">联系方式</h3>
          <p class="section-desc">设置客服的联系电话和QQ</p>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <div class="floating-input">
              <t-input v-model="formData.phone" placeholder="请输入客服电话" size="large" class="modern-input" />
              <label class="floating-label">&nbsp;客服电话</label>
            </div>
          </div>

          <div class="form-group">
            <div class="floating-input">
              <t-input v-model="formData.qq" placeholder="请填写客服QQ" size="large" class="modern-input" />
              <label class="floating-label">&nbsp;客服QQ</label>
            </div>
          </div>
        </div>
      </div>

      <!-- 第二步：二维码配置 -->
      <div class="form-section">
        <div class="section-header">
          <h3 class="section-title">二维码配置</h3>
          <p class="section-desc">上传客服二维码图片</p>
        </div>

        <div class="qr-grid">
          <div class="qr-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{ token: $store.state.user.token }"
                v-model="formData.qr_img"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area">
                  <div v-if="!formData.qr_img || formData.qr_img.length === 0" class="upload-placeholder">
                    <div class="upload-text">点击上传二维码①</div>
                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.qr_img[0].url" alt="二维码①" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>

          <div class="qr-group">
            <div class="custom-upload-container">
              <t-upload
                action="/web/index.php?s=/cloud/Modules/UploadImg"
                :headers="{ token: $store.state.user.token }"
                v-model="formData.qr_img_tow"
                theme="custom"
                accept="image/*"
                :multiple="false"
                class="custom-upload"
              >
                <div class="upload-area">
                  <div v-if="!formData.qr_img_tow || formData.qr_img_tow.length === 0" class="upload-placeholder">
                    <div class="upload-text">点击上传二维码②</div>
                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                  </div>
                  <div v-else class="upload-preview">
                    <img :src="formData.qr_img_tow[0].url" alt="二维码②" class="preview-image">
                    <div class="upload-overlay">
                      <div class="overlay-text">点击重新上传</div>
                    </div>
                  </div>
                </div>
              </t-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <t-button theme="primary" type="submit" class="action-btn submit-btn" size="large">
          <span>保存配置</span>
        </t-button>
      </div>
    </t-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      formData: {
        qr_img: [],
        qr_img_tow: [],
        phone: '',
        qq: '',
        status: false
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      this.$request
        .post("/Customer/getInfo",)
        .then((res) => {
          if (res.info.qr_img == '') {
            this.formData.qr_img = [];
          } else {
            this.formData.qr_img = [{ url: res.info.qr_img }];
          }
          if (res.info.qr_img_tow == '') {
            this.formData.qr_img_tow = [];
          } else {
            this.formData.qr_img_tow = [{ url: res.info.qr_img_tow }];
          }
          this.formData.phone = res.info.phone;
          this.formData.qq = res.info.qq;
          this.formData.status = 1;
        })
        .catch((e) => {
          console.log(e);
        });
    },
    onSubmit({ validateResult }) {
      this.formData.status = 1;
      if (validateResult === true) {
        this.$request
          .post("/Customer/index", { from_data: this.formData })
          .then((res) => {
            if (res.code == 200) {
              this.getList();
              this.$message.success(res.msg);
            } else {
              this.$message.error(res.msg);
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    onReset() {

    },
  },

};
</script>
<style lang="less">
// ==================== 全局CSS变量 ====================
:root {
  --primary-color: #2196F3;
  --primary-dark: #1976D2;
  --primary-light: #f0f8ff;
  --primary-lighter: #f8fbff;
  --primary-border: #e3f2fd;
  --primary-border-hover: #bbdefb;
  --success-color: #4CAF50;
  --success-dark: #388E3C;
  --warning-color: #f57c00;
  --error-color: #f44336;
  --text-primary: #1976D2;
  --text-secondary: #666;
  --background-primary: #ffffff;
  --background-secondary: #f8fbff;
  --background-form: #f0f8ff;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-medium: 0 6px 16px rgba(33, 150, 243, 0.06);
  --shadow-heavy: 0 8px 24px rgba(33, 150, 243, 0.08);
  --radius-small: 10px;
  --radius-medium: 15px;
  --radius-large: 20px;
  --transition: all 0.3s ease;
}
</style>

<style lang="less" scoped>
// ==================== 现代化表单设计系统 ====================

// 页面容器
.modern-form-container {
  min-height: 100vh !important;
  background: #ffffff;
  padding: 40px 20px !important;
  position: relative !important;
  border-radius: 20px;
}

// 页面标题区域
.page-header {
  text-align: center !important;
  margin-bottom: 40px !important;
  position: relative !important;
  z-index: 1 !important;

  .page-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #1976D2 !important;
    margin: 0 0 10px 0 !important;
    text-shadow: 0 2px 4px rgba(33, 150, 243, 0.1) !important;

    @media (max-width: 768px) {
      font-size: 2rem !important;
    }
  }

  .page-subtitle {
    font-size: 1.1rem !important;
    color: #666 !important;
    margin: 0 !important;
    font-weight: 400 !important;
  }
}

// 现代化表单
.modern-form {
  max-width: 80%;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

// 表单区块
.form-section {
  background: #ffffff !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  padding: 40px !important;
  margin-bottom: 30px !important;
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.08) !important;
  border: 1px solid #e3f2fd !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 12px 28px rgba(33, 150, 243, 0.12) !important;
    border-color: #bbdefb !important;
  }

  @media (max-width: 768px) {
    padding: 25px !important;
  }
}

// 区块标题
.section-header {
  margin-bottom: 30px;
  text-align: center;

  .section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
  }

  .section-desc {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.95rem;
  }
}

// 表单网格
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

// 二维码网格 - 居中布局
.qr-grid {
  display: flex;
  justify-content: center;
  gap: 40px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

// 二维码组
.qr-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  min-width: 250px;

  .modern-label {
    text-align: center;
  }

  .upload-container {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

// 表单组
.form-group {
  position: relative;

  &.full-width {
    grid-column: 1 / -1;
  }
}

// 现代化标签
.modern-label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.95rem;
}

// 浮动输入框
.floating-input {
  position: relative;

  .modern-input {
    width: 100%;
    height: 56px;
    border: 2px solid transparent;
    border-radius: var(--radius-small);
    padding: 20px 16px 8px 16px;
    font-size: 1rem;
    background: var(--background-primary);
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    transition: var(--transition);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    }

    &:not(:placeholder-shown)+.floating-label,
    &:focus+.floating-label {
      transform: translateY(-28px) scale(0.85) !important;
      color: #333333 !important;
      background: #ffffff !important;
      padding: 0 8px !important;
      z-index: 15 !important;
      border-radius: 4px !important;
      font-weight: 600;
      box-shadow: 0 0 0 2px #ffffff !important;
    }
  }

  .floating-label {
    position: absolute !important;
    left: 0px !important;
    top: 40% !important;
    transform: translateY(-50%) !important;
    color: #666 !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    z-index: 10 !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

// 上传容器
.upload-container {
  margin-top: 10px;

  .modern-upload {
    border-radius: 12px;
  }
}

// 自定义上传容器
.custom-upload-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 15px;

  .custom-upload {
    width: 200px;
    height: 200px;
  }
}

// 上传区域
.upload-area {
  width: 200px;
  height: 200px;
  border: 2px dashed #e3f2fd;
  border-radius: 15px;
  background: #f8fbff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #2196F3;
    background: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(33, 150, 243, 0.15);
  }
}

// 上传占位符
.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 20px;

  .upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #2196F3;
  }

  .upload-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1976D2;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 0.8rem;
    color: #666;
  }
}

// 上传预览
.upload-preview {
  position: relative;
  width: 100%;
  height: 100%;

  .preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 13px;
  }

  .upload-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(33, 150, 243, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 13px;

    .overlay-text {
      color: white;
      font-weight: 600;
      font-size: 0.9rem;
    }
  }

  &:hover .upload-overlay {
    opacity: 1;
  }
}

// 操作按钮区域
.form-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 32px !important;
  border-radius: var(--radius-medium) !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  transition: var(--transition) !important;
  border: none !important;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }

  &.submit-btn {
    background: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.25) !important;

    &:hover {
      background: var(--primary-dark) !important;
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 16px rgba(33, 150, 243, 0.35) !important;
    }

    &:active {
      transform: translateY(0) !important;
    }
  }
}

// 动画效果
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.form-section {
  animation: slideInUp 0.6s ease-out;

  &:nth-child(2) {
    animation-delay: 0.1s;
  }

  &:nth-child(3) {
    animation-delay: 0.2s;
  }
}

.page-header {
  animation: fadeIn 0.8s ease-out;
}

// 响应式优化
@media (max-width: 768px) {
  .modern-form-container {
    padding: 20px 10px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

// TDesign组件样式覆盖
:deep(.t-input) {
  border-radius: 10px !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04) !important;

  &:focus {
    border-color: #2196F3 !important;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1) !important;
  }

  &:hover {
    border-color: #bbdefb !important;
  }
}

:deep(.t-upload) {
  .t-upload__dragger {
    border-radius: 12px !important;
    border: 2px dashed #e3f2fd !important;
    transition: all 0.3s ease !important;
    background: #f8fbff !important;

    &:hover {
      border-color: #2196F3 !important;
      background: #f0f8ff !important;
    }
  }

  .t-upload__file-list {
    margin-top: 15px;
  }

}

:deep(.t-upload__card) {
  justify-content: center;
}
</style>
