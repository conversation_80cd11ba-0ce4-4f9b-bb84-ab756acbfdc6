import { DashboardIcon } from 'tdesign-icons-vue';
import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/dashboard',
    component: Layout,
    redirect: '/dashboard/base',
    name: 'dashboard',
    meta: {
      title: '首页',
      icon: DashboardIcon,
    },
    children: [
      {
        path: 'base',
        name: 'DashboardBase',
        component: () => import('@/pages/dashboard/base/index.vue'),
        meta: { title: '数据统计' },
      },
      {
        path: 'edit_pwd',
        name: 'DashboardEditPwd',
        component: () => import('@/pages/dashboard/base/edit_pwd.vue'),
        meta: { title: '数据统计',hidden:true },
      }

    ],
  },
];
