import {ImageOffIcon, Edit1Icon ,AppIcon,UsergroupIcon,SettingIcon} from 'tdesign-icons-vue';
import Layout from '@/layouts/index.vue';

export default [
  {
    path: '/modules',
    name: 'modules',
    component: Layout,
    redirect: '/modules/index',
    meta: {title: '应用管理', icon: AppIcon},
    children: [
      {
        path: 'index',
        name: 'ModulesIndex',
        component: () => import('@/pages/modules/index.vue'),
        meta: {title: '应用管理'},
      }
    ],
  },
  {
    path: '/platform',
    name: 'platform',
    component: Layout,
    redirect: '/platform/index',
    meta: {title: '平台管理', icon: ImageOffIcon},
    children: [
      {
        path: 'index',
        name: 'PlatformIndex',
        component: () => import('@/pages/platform/index.vue'),
        meta: {title: '所有平台'},
      },
      {
        path: 'add',
        name: 'PlatformAdd',
        component: () => import('@/pages/platform/add.vue'),
        meta: {title: '新建平台'},
      }, {
        path: 'edit',
        name: 'PlatformEdit',
        component: () => import('@/pages/platform/edit.vue'),
        meta: {title: '编辑平台', hidden: true},
      }, {
        path: 'upload',
        name: 'PlatformUpload',
        component: () => import('@/pages/platform/upload.vue'),
        meta: {title: '上传小程序', hidden: true},
      }
    ],
  },
  {
    path: '/user',
    name: 'user',
    component: Layout,
    redirect: '/user/index',
    meta: {title: '用户管理', icon: UsergroupIcon},
    children: [
      {
        path: 'index',
        name: 'UserIndex',
        component: () => import('@/pages/user/index.vue'),
        meta: {title: '用户管理'},
      },
      {
        path: 'add',
        name: 'UserAdd',
        component: () => import('@/pages/user/add.vue'),
        meta: {title: '新增用户'},
      },
      {
        path: 'edit',
        name: 'UserEdit',
        component: () => import('@/pages/user/edit.vue'),
        meta: {title: '编辑用户', hidden: true},
      }
    ],
  },
  {
    path: '/customer',
    name: 'customer',
    component: Layout,
    redirect: '/customer/index',
    meta: {title: '系统设置', icon: SettingIcon},
    children: [
      {
        path: 'index',
        name: 'CustomerIndex',
        component: () => import('@/pages/customer/index.vue'),
        meta: {title: '客服设置'},
      },
      {
        path: 'settings',
        name: 'CustomerSettings',
        component: () => import('@/pages/customer/settings.vue'),
        meta: {title: '站点设置'},
      },
      {
        path: 'announcement',
        name: 'CustomerAnnouncement',
        component: () => import('@/pages/customer/announcement.vue'),
        meta: {title: '系统公告'},
      },
      {
        path: 'log_info',
        name: 'CustomerLogInfo',
        component: () => import('@/pages/customer/log_info.vue'),
        meta: {title: '操作日志'},
      },
      {
        path: 'information',
        name: 'CustomerInformation',
        component: () => import('@/pages/customer/information.vue'),
        meta: {title: '系统信息'},
      },
      {
        path: 'sql_data',
        name: 'CustomerSqlData',
        component: () => import('@/pages/customer/sql_data.vue'),
        meta: {title: '数据导入'},
      },
      {
        path: 'info',
        name: 'CustomerInfo',
        component: () => import('@/pages/dashboard/base/info.vue'),
        meta: { title: '公告详情',hidden:true },
      },
      {
        path: 'add_announcement',
        name: 'AddCustomer',
        component: () => import('@/pages/customer/add_announcement.vue'),
        meta: { title: '新增公告',hidden:true },
      },
      {
        path: 'edit_announcement',
        name: 'EditCustomer',
        component: () => import('@/pages/customer/edit_announcement.vue'),
        meta: { title: '编辑公告',hidden:true },
      }
    ],
  }
];
