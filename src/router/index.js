import VueRouter from 'vue-router';

import baseRouters from './modules/base';
import componentsRouters from './modules/components';
const env = import.meta.env.MODE || 'development';

// 存放动态路由
export const asyncRouterList = [...baseRouters, ...componentsRouters];

// 存放固定的路由
const defaultRouterList = [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '后台登陆页面',
    },
    component: () => import('@/pages/login/index.vue'),
  },
  {
    path: '/install',
    name: 'install',
    meta: {title: '系统安装'},
    component: () => import('@/pages/install/index.vue'),
  },
  {
    path: '*',
    redirect: '/dashboard/base',
  },
  ...asyncRouterList,
];

const createRouter = () =>
  new VueRouter({
    mode: 'hash',
    base: env === 'site' ? '/starter/vue/' : null,
    routes: defaultRouterList,
    scrollBehavior() {
      return { x: 0, y: 0 };
    },
  });

const router = createRouter();

export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
