<template>
  <router-view :class="[mode]" />
</template>

<script>
import Vue from 'vue';
import config from '@/config/style';

export default Vue.extend({
  computed: {
    mode() {
      return this.$store.getters['setting/mode'];
    },
  },
  mounted() {
    //this.$store.dispatch('setting/changeTheme', { ...config });
  },
});
</script>
<style lang="less">
.t-default-menu .t-menu__item{
  height: 50px;
  font-size: 16px;
}
/* 全局样式文件（例如：main.css） */
:root {
  --td-brand-color: #2196F3; /* 修改品牌色 */
}
</style>
