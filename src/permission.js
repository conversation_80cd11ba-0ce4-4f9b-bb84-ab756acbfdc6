import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style

import store from '@/store';
import router from '@/router';
const whiteListRouters = store.getters['permission/whiteListRouters'];
NProgress.configure({ showSpinner: false });
router.beforeEach(async (to, from, next) => {
  NProgress.start();
  document.title = to.meta.title;
  const token = store.getters['user/token'];
  if (token) {
    if (to.path === '/login') {
      // setTimeout(() => {
      //   store.dispatch('user/logout');
      //   store.dispatch('permission/restore');
      // });
      next('/dashboard/base');
      return;
    }
    await store.dispatch('user/getUserInfo');
    await store.dispatch('permission/initRoutes', store.getters['user/roles']);
    const roles = store.getters['user/roles'];
    if (roles && roles.length > 0) {
      if(roles[0]==='ALL_ROUTERS'){
        next();
      }else{
        if (roles.indexOf(to.name) !== -1) {
          next();
        } else {
          next('/dashboard/base');
        }
      }
      NProgress.done();
    } else {
      try {


        next({ ...to });
      } catch (error) {
        await store.commit('user/removeToken');
        next(`/login`);
      }
      NProgress.done();
    }
  } else {
    if (whiteListRouters.indexOf(to.path) !== -1) {
      next();
    } else {
      next(`/login`);
    }
    NProgress.done();
  }
});

router.afterEach(() => {
  NProgress.done();
});
