import {loadEnv} from 'vite';
import {viteMockServe} from 'vite-plugin-mock';
import {createVuePlugin} from 'vite-plugin-vue2';
import {createSvgPlugin} from 'vite-plugin-vue2-svg';
import path from 'path';
import { createHash } from 'crypto';

const CWD = process.cwd();


const nowTimeInMilliseconds = new Date().getTime();
const nowTimeMd5 = createHash('md5').update(nowTimeInMilliseconds.toString()).digest('hex');


export default ({mode}) => {
  const {VITE_BASE_URL} = loadEnv(mode, CWD);

  return {
    base: VITE_BASE_URL,
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
    },

    css: {
      preprocessorOptions: {
        less: {
          modifyVars: {},
        },
      },
    },

    plugins: [
      createVuePlugin({
        jsx: true,
      }),
      viteMockServe({
        mockPath: 'mock',
        localEnabled: true,
      }),
      createSvgPlugin(),
    ],
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      cssCodeSplit: false,
      rollupOptions: {
        output: {
          // 最小化拆分包
          manualChunks: (id) => {
            if (id.includes('node_modules')) {
              return id.toString().split('node_modules/')[1].split('/')[0].toString();
            }
          },
          // 用于从入口点创建的块的打包输出格式[name]表示文件名,[hash]表示该文件内容hash值
          entryFileNames: `static/${nowTimeMd5}/js/[name].[hash].js`,
          // 用于命名代码拆分时创建的共享块的输出命名
          chunkFileNames: `static/${nowTimeMd5}/js/[name].[hash].js`,
          // 用于输出静态资源的命名，[ext]表示文件扩展名
          assetFileNames: `static/${nowTimeMd5}/[ext]/[name].[hash].[ext]`,
        },
      },
    },
    server: {
      proxy: {
        '/cloud': {
          // 用于开发环境下的转发请求
          // 更多请参考：https://vitejs.dev/config/#server-proxy
          target: 'https://yun.inotnpc.com/web/index.php?s=/',
          pathRewrite: {
            '^/cloud': ''
          },
          changeOrigin: true,
        },
      },
    },
  };
};
