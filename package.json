{"name": "@tencent/tdesign-vue-starter", "version": "0.6.2", "scripts": {"dev:mock": "vite --open --mode mock", "dev": "vite --open --mode development", "dev:linux": "vite --mode development", "build:test": "vite build --mode test", "build": "vite build --mode release", "build:site": "vite build --mode site", "site:preview": "npm run build && cp -r dist _site", "preview": "vite preview", "lint": "eslint --ext .vue,.js,.jsx,.ts,.tsx ./ --max-warnings 0", "lint:fix": "eslint --ext .vue,.js,jsx,.ts,.tsx ./ --max-warnings 0 --fix", "stylelint": "stylelint src/**/*.{html,vue,sass,less}", "stylelint:fix": "stylelint --cache --fix src/**/*.{html,vue,vss,sass,less}", "prepare": "husky install", "test": "echo \"no test specified,work in process\"", "test:coverage": "echo \"no test:coverage specified,work in process\""}, "dependencies": {"@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^1.0.2", "axios": "^1.7.7", "dayjs": "^1.11.13", "echarts": "^5.5.1", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qrcode.vue": "^1.7.0", "tdesign-icons-vue": "^0.3.4", "tdesign-vue": "^1.12.1", "tvision-color": "~1.6.0", "typescript": "^5.6.3", "vue": "~2.6.14", "vue-clipboard2": "^0.3.1", "vue-router": "^3.6.5", "vue-verify-code": "^1.0.5", "vuex": "^3.6.2", "vuex-router-sync": "^5.0.0"}, "devDependencies": {"@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@types/vue-color": "^2.4.3", "@typescript-eslint/eslint-plugin": "^8.8.1", "@typescript-eslint/parser": "^8.8.1", "commitizen": "^4.3.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.28.0", "husky": "^9.1.6", "less": "^4.2.0", "lint-staged": "^15.2.10", "mockjs": "^1.1.0", "prettier": "^3.3.3", "stylelint": "~13.13.1", "stylelint-config-prettier": "~9.0.3", "stylelint-less": "1.0.5", "stylelint-order": "~4.1.0", "terser": "^5.36.0", "vite": "^4.5.5", "vite-plugin-mock": "^3.0.2", "vite-plugin-theme": "^0.8.6", "vite-plugin-vue2": "^2.0.3", "vite-plugin-vue2-svg": "~0.4.0", "vue-template-compiler": "~2.6.14"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"pre-commit": "lint-staged", "prepare-commit-msg": "exec < /dev/tty && git cz --hook || true", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["prettier --write", "npm run lint:fix"], "*.{html,vue,vss,sass,less}": ["npm run stylelint:fix"]}}