{"compilerOptions": {"target": "es2015", "module": "esnext", "lib": ["esnext", "dom"], "allowJs": true, "checkJs": false, "noImplicitAny": false, "isolatedModules": false, "experimentalDecorators": true, "esModuleInterop": true, "noImplicitThis": false, "strictNullChecks": false, "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "jsx": "preserve", "noEmit": true, "baseUrl": "./", "paths": {"@/*": ["./src/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "compileOnSave": false}